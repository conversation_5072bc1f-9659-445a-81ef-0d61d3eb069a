{
    "workbench.startupEditor": "newUntitledFile",
    //==============================java配置==============================
    "java.errors.incompleteClasspath.severity": "ignore",
    // 启用/禁用“自动构建”
    "java.autobuild.enabled": true,
    // 启用/禁用代码完成支持
    "java.completion.enabled": true,
    //关闭debug前强制编译
    "java.debug.settings.forceBuildBeforeLaunch": false,
    // 启动时自动显示构建状态
    "java.showBuildStatusOnStart.enabled": true,
    "java.jdt.ls.java.home": "D:\\Program\\Java\\jdk-21",
    "concourse.ls.java.home": "D:\\Program\\Java\\jdk-21",
    "spring-boot.ls.java.home": "D:\\Program\\Java\\jdk-21",
    "cloudfoundry-manifest.ls.java.home": "D:\\Program\\Java\\jdk-21",
    // 跟踪 VS Code 与 Java 语言服务器之间的通信。
    "java.trace.server": "messages",
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1536m -Xms768m",
    "java.refactor.renameFromFileExplorer": "autoApply",
    "java.project.importHint": false,
    // 通过glob模式将文件夹排除在导入之外。用来否定模式以允许子文件夹导入。你必须要包括一个父目录。顺序很重要。
    "java.import.exclusions": [
        "**/node_modules/**",
        "**/.metadata/**",
        "**/archetype-resources/**",
        "**/META-INF/maven/**"
    ],
    //排除Java语言服务器刷新的文件和文件夹，这可以提高整体性能。例如，["node_modules",".git"]
    "java.project.resourceFilters": [
        "node_modules",
        ".git",
        ".idea",
        ".vscode"
    ],
    //指定是否在项目根目录下生成项目元数据文件（.project、.classpath、.factorypath、.settings/）。缺省值为 :false
    "java.import.generatesMetadataFilesAtProjectRoot": true,
    //指定Java扩展所使用的Gradle分布。
    // 启用/禁用Gradle导入器。
    "java.import.gradle.enabled": false,
    "java.eclipse.downloadSources": true,
    "java.maven.downloadSources": true,
    //自动导入包
    "java.import.maven.enabled": true,
    "java.dependency.syncWithFolderExplorer": true,
    "java.server.launchMode": "Standard",
    //maven、gradle的配置文件变更后自动更新
    "java.configuration.updateBuildConfiguration": "automatic",
    //java源文件路径
    "java.project.sourcePaths": [
        "src/main/java",
        "src/test/java",
    ],
    "java.project.outputPath": "bin",
    //配置用于将本地库引用到 Java 项目的 glob 模式。
    "java.project.referencedLibraries": [
        "lib/**/*.jar"
    ],
    "java.configuration.maven.globalSettings": "D:\\Program\\apache-maven-3.6.1\\conf\\settings.xml",
    "java.configuration.maven.userSettings": "D:\\Program\\apache-maven-3.6.1\\conf\\settings.xml",
    // 指定“mvn”可执行文件的绝对路径。当此值为空时，它会根据“maven.executable.preferMavenWrapper”的值尝试使用“mvn”或“mvnw”
    "maven.executable.path": "D:\\Program\\apache-maven-3.6.1\\bin\\mvn.cmd",
    "maven.settingsFile": "D:\\Program\\apache-maven-3.6.1\\conf\\settings.xml",
    "maven.executable.preferMavenWrapper": false,
    "maven.pomfile.autoUpdateEffectivePOM": true,
    "maven.terminal.useJavaHome": true,
    //默认执行命令选项：-DarchetypeCatalog=internal来加快构建速度
    "maven.executable.options": "-DarchetypeCatalog=internal",
    //是否启用依赖冲突诊断
    "maven.dependency.enableConflictDiagnostics": true,
    //指定用于查找 pom.xml 文件的 glob 模式。
    "maven.pomfile.globPattern": "**/pom.xml",
    //强制更新快照/版本。默认值为 。false
    "java.maven.updateSnapshots": true,
    "maven.terminal.customEnv": [
        {
            "environmentVariable": "JAVA_HOME",
            "value": "D:\\Program\\Java\\jdk-21"
        }
    ],
    "maven.excludedFolders": [
        "**/.*",
        "**/node_modules",
        "**/target",
        "**/bin",
        "**/archetype-resources"
    ],
    "[json]": {
        "editor.quickSuggestions": {
            "strings": true
        },
        "editor.suggest.insertMode": "replace"
    },
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-21",
            "path": "D:\\Program\\Java\\jdk-21",
            "default": true
        }
    ],
    "java.completion.filteredTypes": [
        "java.awt.*",
        "com.sun.*",
        "sun.*",
        "jdk.*",
        "org.graalvm.*",
        "io.micrometer.shaded.*"
    ],
    //==============================spring配置==============================
    "spring.initializr.serviceUrl": [
        "https://start.spring.io/",
        "https://start.aliyun.com"
    ],
    "spring.initializr.defaultLanguage": "Java",
    "spring.initializr.defaultPackaging": "JAR",
    "spring.initializr.defaultJavaVersion": "8",
    "spring.initializr.defaultArtifactId": "spring-demo",
    "spring.initializr.defaultGroupId": "com.xinpa",
    "spring.initializr.defaultOpenProjectMethod": "Add to Workspace",
    // spring-boot 配置用户设置占用内存数
    "spring-boot.ls.java.vmargs": [
        "-Xms512m",
        "-Xmx1024m",
        "-XX:CompressedClassSpaceSize=128m",
        "-XX:MetaspaceSize=200m",
        "-XX:MaxMetaspaceSize=200m"
    ],
    "cloudfoundry-manifest.ls.java.vmargs": [
        "-Xms512m",
        "-Xmx1024m",
        "-XX:CompressedClassSpaceSize=128m",
        "-XX:MetaspaceSize=200m",
        "-XX:MaxMetaspaceSize=200m"
    ],
    //==============================vscode常用配置==============================
    "editor.suggestSelection": "first",
    "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue",
    //这些文件将不会显示在工作空间中
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "node_modules/": true,
        "**/*.js": {
            "when": "$(basename).ts"
        },
        "**/*.code-workspace": true,
        "**/.classpath": false,
        "**/.project": false,
        "**/.factorypath": false,
        "**/.settings": false
    },
    //不索引一些不必要索引的大文件夹
    "files.watcherExclude": {
        "**/.git/**": true,
        "**/node_modules/**": true,
        "**/tmp/**": true,
        "**/bower_components/**": true,
        "**/dist/**": true,
        "**/target/**": true,
        "**/logs/**": true,
    },
    // 在使用搜索功能时，将这些文件夹/文件排除在外
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/target": true,
        "**/logs": true,
    },
    "vsicons.dontShowNewVersionMessage": true,
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell"
        },
        "Command Prompt": {
            "path": [
                "${env:windir}\\Sysnative\\cmd.exe",
                "${env:windir}\\System32\\cmd.exe"
            ],
            "args": [],
            "icon": "terminal-cmd"
        },
        "Git Bash": {
            "source": "Git Bash"
        },
        "default-cmd": {
            "path": "C:\\Windows\\System32\\cmd.exe",
            "args": []
        },
        "Ubuntu-22.04 (WSL)": {
            "path": "C:\\Windows\\System32\\wsl.exe",
            "args": [
                "-d",
                "Ubuntu-22.04"
            ]
        },
        "CentOS8 (WSL)": {
            "path": "C:\\Windows\\System32\\wsl.exe",
            "args": [
                "-d",
                "CentOS8"
            ]
        }
    },
    "terminal.integrated.defaultProfile.windows": "default-cmd",
    // 启用后，编辑器将尝试在打开文件时猜测字符集编码
    "files.autoGuessEncoding": true,
    //失去焦点后自动保存
    "files.autoSave": "onFocusChange",
    // 设置自定义文件图标关联。
    "files.associations": {
        "*.wpy": "vue",
        "*.cjson": "jsonc",
        "*.wxss": "css",
        "*.wxs": "javascript",
        "*.java": "java"
    },
    "emmet.includeLanguages": {
        "wxml": "html"
    },
    // 控制在资源管理器内拖放移动文件或文件夹时是否进行确认。
    "explorer.confirmDragAndDrop": false,
    // 控制资源管理器是否在把文件删除到废纸篓时进行确认。
    "explorer.confirmDelete": false,
    //控制是否在搜索中跟踪符号链接
    "search.followSymlinks": false,
    "boot-java.rewrite.reconcile": true,
    // 控制编辑器是否应在对屏幕阅读器进行了优化的模式下运行。设置为“开”将禁用自动换行。
    "editor.accessibilitySupport": "off",
    //保存文件时是否自动格式化
    "editor.formatOnSave": false,
    "editor.largeFileOptimizations": false,
    "git.openRepositoryInParentFolders": "never",
    "[xml]": {
        "editor.defaultFormatter": "DotJoshJohnson.xml"
    },
    "workbench.iconTheme": "vscode-icons",
    "editor.detectIndentation": false,
    "editor.insertSpaces": false,
    //html标签自动重命名
    "editor.linkedEditing": true,
    //复制代码时复制纯文本而不是连语法高亮都复制了
    "editor.copyWithSyntaxHighlighting": false,
    //编辑器中显示不可见的控制字符
    "editor.renderControlCharacters": true,
    //启用默认的java格式化
    "java.format.enabled": true,
    "[java]": {
        "editor.formatOnSave": false,
        "editor.defaultFormatter": null
    },
	"terminal.integrated.defaultProfile.windows": "Command Prompt",
	"terminal.integrated.env.windows": {
		"LANG": "zh_CN.UTF-8"
	},
	"terminal.integrated.encoding": "utf8",
	"files.encoding": "utf8"
	
}