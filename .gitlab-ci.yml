stages:
  - build
variables:
  IMAGE: "$TEST_DOCKER_REGISTRY_INNER/$CI_PROJECT_NAME-$CI_COMMIT_BRANCH"
  APP_NAME: $CI_PROJECT_NAME
  APP_PORT: 5006
  APP_REPLICAS: 1
test-build-job:
  stage: build
  variables:
    RUN_ENV: gok-business-test
  before_script:
    - docker login -u "$TEST_DOCKER_REGISTRY_USER" -p "$TEST_DOCKER_REGISTRY_PASSWORD" "$TEST_DOCKER_REGISTRY_INNER"
  script:
    - mvn clean package -Ptest -Dmaven.test.skip=true
    - export CICD_IMAGE=$IMAGE CICD_EXECUTION_SEQUENCE=$(date +%Y%m%d%H%M)
    - docker build --pull -t "$IMAGE:$CICD_EXECUTION_SEQUENCE" .
    - docker push "$IMAGE:$CICD_EXECUTION_SEQUENCE"
    - envsubst '$APP_NAME$APP_PORT$RUN_ENV$APP_REPLICAS$CICD_IMAGE$CICD_EXECUTION_SEQUENCE' <deployment.yaml>deployment_tmp.yaml
    - mv -f deployment_tmp.yaml deployment.yaml
    - kubectl apply -f deployment.yaml
  only:
    - test
  tags:
    - p_test

pre-build-job:
  stage: build
  variables:
    RUN_ENV: gok-business-pre
  before_script:
    - docker login -u "$TEST_DOCKER_REGISTRY_USER" -p "$TEST_DOCKER_REGISTRY_PASSWORD" "$TEST_DOCKER_REGISTRY_INNER"
  script:
    - mvn clean package -Ppre -Dmaven.test.skip=true
    - export CICD_IMAGE=$IMAGE CICD_EXECUTION_SEQUENCE=$(date +%Y%m%d%H%M)
    - docker build --pull -t "$IMAGE:$CICD_EXECUTION_SEQUENCE" .
    - docker push "$IMAGE:$CICD_EXECUTION_SEQUENCE"
    - envsubst '$APP_NAME$APP_PORT$RUN_ENV$APP_REPLICAS$CICD_IMAGE$CICD_EXECUTION_SEQUENCE' <deployment.yaml>deployment_tmp.yaml
    - mv -f deployment_tmp.yaml deployment.yaml
    - kubectl apply -f deployment.yaml
  only:
    - pre
  tags:
    - p_test

dev-build-job:
  stage: build
  variables:
    RUN_ENV: gok-business-dev
  before_script:
    - docker login -u "$TEST_DOCKER_REGISTRY_USER" -p "$TEST_DOCKER_REGISTRY_PASSWORD" "$TEST_DOCKER_REGISTRY_INNER"
  script:
    - mvn clean package -Pdev -Dmaven.test.skip=true
    - export CICD_IMAGE=$IMAGE CICD_EXECUTION_SEQUENCE=$(date +%Y%m%d%H%M)
    - docker build --pull -t "$IMAGE:$CICD_EXECUTION_SEQUENCE" .
    - docker push "$IMAGE:$CICD_EXECUTION_SEQUENCE"
    - envsubst '$APP_NAME$APP_PORT$RUN_ENV$APP_REPLICAS$CICD_IMAGE$CICD_EXECUTION_SEQUENCE' <deployment.yaml>deployment_tmp.yaml
    - mv -f deployment_tmp.yaml deployment.yaml
    - kubectl apply -f deployment.yaml
  only:
    - dev
  tags:
    - p_dev

prod-build-job:
  stage: build
  variables:
    RUN_ENV: gok-business-prod
  before_script:
    - docker login -u "$PROD_DOCKER_REGISTRY_USER" -p "$PROD_DOCKER_REGISTRY_PASSWORD" "$PROD_DOCKER_REGISTRY_INNER"
  script:
    - mvn clean package -Pmaster -Dmaven.test.skip=true
    - export CICD_IMAGE="$PROD_DOCKER_REGISTRY_INNER/$CI_PROJECT_NAME-$CI_COMMIT_BRANCH" CICD_EXECUTION_SEQUENCE=$(date +%Y%m%d%H%M)
    - docker build -t "$CICD_IMAGE:$CICD_EXECUTION_SEQUENCE" .
    - docker push "$CICD_IMAGE:$CICD_EXECUTION_SEQUENCE"
    - envsubst '$APP_NAME$APP_PORT$RUN_ENV$APP_REPLICAS$CICD_IMAGE$CICD_EXECUTION_SEQUENCE' <deployment.yaml>deployment_tmp.yaml
    - mv -f deployment_tmp.yaml deployment.yaml
    - kubectl apply -f deployment.yaml
  only:
    - master
  tags:
    - p_master
