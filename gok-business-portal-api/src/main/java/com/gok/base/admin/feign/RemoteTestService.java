package com.gok.base.admin.feign;

import com.gok.base.admin.common.TalentConstants;
import com.gok.components.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 远程测试服务
 *
 * <AUTHOR>
 * @date 2018/6/28
 */
@FeignClient(contextId = "remoteTestService", value = TalentConstants.TALENT_SERVICE)
public interface RemoteTestService {

	/**
	 * test
	 * @param from 是否内部调用
	 * @return succes、false
	 */
	@PostMapping("/test")
	R<Boolean> test(@RequestHeader(TalentConstants.FROM) String from);

}
