package com.gok.base.admin.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取未关闭的bug
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnclosedBugsVo {


    /**
     * ID
     */
    private Long id;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 处理情况（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    private Integer handlingSituation;

    /**
     * 处理情况Str
     */
    private String handlingSituationStr;
}
