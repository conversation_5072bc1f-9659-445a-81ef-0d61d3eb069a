package com.gok.base.admin.feign;

import com.gok.base.admin.vo.CmsContentPublishVo;
import com.gok.base.admin.vo.UnclosedBugsVo;
import com.gok.base.admin.vo.VodTransProgressVo;
import com.gok.base.admin.common.TalentConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 远程门户服务
 *
 * <AUTHOR>
 * @date 2018/6/28
 */
@FeignClient(contextId = "remotePortalService", value = TalentConstants.PORTAL_SERVICE)
public interface RemotePortalService {

	/**
	 * 获取转码未完成的视频
	 * @param from 是否内部调用
	 * @return succes、false
	 */
	@PostMapping("/vod/trans/notCompleted")
	List<VodTransProgressVo> getTransNotCompleted(@RequestHeader(TalentConstants.FROM) String from);

	/**
	 * 处理获取禅道关闭的任务
	 * @param from 是否内部调用
	 * @return succes、false
	 */
	@PostMapping("/projectFeedBack/get/unclosedBugs")
	List<UnclosedBugsVo> getUnclosedBugs(@RequestHeader(TalentConstants.FROM) String from);

	/**
	 * 按发布时间发送消息
	 *
	 * @param from 是否内部调用
	 * @return {@link List}<{@link CmsContentPublishVo}>
	 */
	@PostMapping("/cmsContent/sendMsgByPublishTime")
	List<CmsContentPublishVo> sendMsgByPublishTime(@RequestHeader(TalentConstants.FROM) String from);


}
