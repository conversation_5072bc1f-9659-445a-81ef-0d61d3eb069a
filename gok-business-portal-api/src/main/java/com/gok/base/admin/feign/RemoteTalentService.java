package com.gok.base.admin.feign;

import com.gok.base.admin.common.TalentConstants;
import com.gok.components.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * description: 数据大屏统计任务
 *
 * <AUTHOR> jy.chen
 * @version : 1.0
 * @since : 2023-06-25
 */
@FeignClient(contextId = "remoteTalentService", value = TalentConstants.TALENT_SERVICE)
public interface RemoteTalentService {

	/**
	 * 执行定时统计任务
	 * @param from 是否内部调用
	 *
	 * @return success、false
	 */
	@PostMapping("/statistic-report/executeTask")
	R<Boolean> executeTask(@RequestHeader(TalentConstants.FROM) String from);

	/**
	 * 执行全量同步es任务
	 *
	 * @param day 具体是日期
	 * @param from 是否内部调用
	 * @return success、false
	 */
	@PostMapping("/statistic-report/syncEsTask")
	R<Boolean> syncEsTask(@RequestParam("day") String day, @RequestHeader(TalentConstants.FROM) String from);

}
