package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 内容发布 VO
 *
 * <AUTHOR>
 * @date 2/11/2023
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsContentPublishVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 业务分类（0规章制度、1办事指南和表格、2公司新闻、3行业资讯）
     *
     */
    private Integer businessType;

    /**
     * 内容类型（0图文、1H5链接、2视频）
     *
     */
    private Integer contentType;

    /**
     * 内容类别（0轮播图、1通知公告、2制度文件、3培训计划、4新闻资讯、5国科周报）
     *
     */
    private Integer contentCategory;

    /**
     * 发布者id
     */
    private Long publisherId;

    /**
     * 发布者名称
     */
    private String publisherName;

    /**
     * 定时发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 上架状态（0已上架、1未上架）
     *
     */
    private Integer status;

    /**
     * 正文
     */
    private String contentText;

    /**
     * 关联内容id
     */
    private Long relationContentId;

    /**
     * 链接地址
     */
    private String linkAddress;

    /**
     * 封面图url
     */
    private String coverImageUrl;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建者名称
     */
    private String createUserName;


    /**
     * 上架时间
     */
    private LocalDateTime launchTime;

}