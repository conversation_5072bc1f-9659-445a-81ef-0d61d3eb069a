package com.gok.pboot.portal.service;


import com.gok.bcp.message.res.BasePageRes;
import com.gok.bcp.message.vo.BcpMessageVO;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.MessagePageDto;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * message服务
 *
 * <AUTHOR>
 * @date 26/10/2023
 */
public interface IMessageService {

    /**
     * 分页获取
     *
     * @param req 要求
     * @return {@link R}<{@link BasePageRes}<{@link BcpMessageVO}>>
     */
    R<BasePageRes<BcpMessageVO>> page(MessagePageDto req);

    /**
     * 按 ID 更新邮件(设置为已读)
     *
     * @param id 编号
     * @return {@link R}
     */
    R  updateMailById(Long id);

    /**
     * 设置全部已读
     *
     * @return {@link R}
     */
    R  mailReadByUserId();

    /**
     *
     * 通过用户id查询站内信未拉取的消息
     *
     * @return {@link R}<{@link List}<{@link BcpMessageVO}>>
     */
    R<List<BcpMessageVO>> getMailUnPullListByUserId();

    }
