package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.PortalApplicationManagePageDto;
import com.gok.pboot.portal.dto.PortalApplicationManageSaveOrUpdateDto;
import com.gok.pboot.portal.service.IPortalApplicationManageService;
import com.gok.pboot.portal.vo.PortalApplicationManageBoxVo;
import com.gok.pboot.portal.vo.PortalApplicationManageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 应用管理表
 * <AUTHOR>
 * @description 应用管理表
 * @menu 应用管理
 * @since 2023-08-01
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/portalApplicationManage")
@Api(tags = "应用管理表")
public class PortalApplicationManageController {

    @Resource
    private IPortalApplicationManageService portalApplicationManageService;

    /**
     * 分页模糊查询
     *
     * @param page    分页条件
     * @param pageDto 页面 dto
     * @return R
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页模糊查询", notes = "可以根据上下线状态，分类，应用名称模糊查询")
    public R<Page<PortalApplicationManageVo>> queryAll(Page page, PortalApplicationManagePageDto pageDto) {
        return portalApplicationManageService.queryAllManage(page, pageDto);
    }

    /**
     * 上下架
     *
     * @param id 应用Id
     * @return R
     */
    @GetMapping("/onOrOff/{id}")
    @ApiOperation(value = "上下架", notes = "上架点击变下架，下架点击变上架")
    public R onOrOff(@PathVariable Long id) {
        return portalApplicationManageService.onOrOff(id);
    }

    /**
     * 新增接口
     *
     * @param dto 新增实体类
     * @return R
     */
    @PostMapping("/save")
    public R save(@Valid @RequestBody PortalApplicationManageSaveOrUpdateDto dto) {
        return portalApplicationManageService.saveDto(dto);
    }

    /**
     * 修改
     *
     * @param dto 修改实体类
     * @return R
     */
    @PostMapping("/update")
    public R update(@Valid @RequestBody PortalApplicationManageSaveOrUpdateDto dto) {
        return portalApplicationManageService.updateDto(dto);
    }

    /**
     * 下架后的删除操作
     *
     * @param id 应用id
     * @return R
     */
    @DeleteMapping("/remove/{id}")
    public R remove(@PathVariable Long id) {
        return portalApplicationManageService.remove(id);
    }

    /**
     * 未上架列表
     *
     * @return {@link R}<{@link List}<{@link PortalApplicationManageBoxVo}>>
     */
    @GetMapping("/offShelfList")
    @ApiOperation(value = "未上架列表", notes = "下拉框显示未上架应用集合")
    public R<List<PortalApplicationManageBoxVo>> offShelfList() {
        return R.ok(portalApplicationManageService.offShelfList());
    }

    /**
     * 获取单个应用信息
     *
     * @param id 编号
     * @return {@link R}<{@link PortalApplicationManageVo}>
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "获取单个应用信息", notes = "用于应用管理编辑窗口")
    public R<PortalApplicationManageVo> getOne(@PathVariable Long id) {
        return R.ok(portalApplicationManageService.getOne(id));
    }
}
