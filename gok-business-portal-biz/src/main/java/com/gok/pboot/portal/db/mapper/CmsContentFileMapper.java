package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.CmsContentFile;
import com.gok.pboot.portal.vo.VodTransProgressVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 内容附件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
public interface CmsContentFileMapper extends BaseMapper<CmsContentFile> {

    /**
     * 根据内容Id查询附件列表
     *
     * @param contentId 内容 ID
     * @return {@link List}<{@link CmsContentFile}>
     */
    List<CmsContentFile> selByContentId(@Param("contentId") Long contentId);

    /**
     * 根据内容Id查询附件列表
     *
     * @param contentIdList 内容id集合
     * @return {@link List}<{@link CmsContentFile}>
     */
    List<CmsContentFile> selByContentIdList(@Param("contentIdList") List<Long> contentIdList);

    /**
     * 根据id分页查询内容附件信息
     *
     * @param page      分页请求
     * @param contentId 文件id
     * @return {@link Page}<{@link CmsContentFile}>
     */
    Page<CmsContentFile> findPage(Page page, @Param("contentId") Long contentId);

    /**
     * 根据对象类集合批量插入
     *
     * @param entityList 实体类集合
     */
    void batchSave(List<CmsContentFile> entityList);

    /**
     * 根据内容id进行逻辑删除
     *
     * @param contentId 内容id
     * @return int
     */
    int logicDeleteByContentId(@Param("contentId") Long contentId);

    /**
     * 根据id集合进行逻辑删除
     *
     * @param idList id集合
     * @return int
     */
    int logicDeleteByIdList(@Param("idList") List<Long> idList);

    /**
     * 查询转码未完成的视频文件
     *
     * @return {@link List}<{@link CmsContentFile}>
     */
    List<CmsContentFile> selTransNotCompleted();

    /**
     * 批量编辑视频转码进度
     *
     * @param transProgressList TRANS 进度列表
     */
    void batchEditProgress(@Param("transProgressList") List<VodTransProgressVo> transProgressList);

    /**
     * 根据视频key去更新转码进度
     *
     * @param videoKey     视频key
     * @param transProcess 转码进度
     */
    void updateTransProgressByKey(@Param("videoKey") String videoKey, @Param("transProcess") Long transProcess);
}
