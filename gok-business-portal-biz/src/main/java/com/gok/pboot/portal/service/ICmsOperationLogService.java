package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.portal.db.entity.CmsOperationLog;
import com.gok.pboot.portal.dto.CmsOperationLogSaveDto;
import com.gok.pboot.portal.enums.OperationTypeEnum;
import com.gok.pboot.portal.vo.CmsOperationLogVo;

import java.util.List;

/**
 * <p>
 * 内容操作记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
public interface ICmsOperationLogService extends IService<CmsOperationLog> {

    /**
     * 通过内容的id获取操作记录
     *
     * @param contentId 内容id
     * @param page      页面
     * @return {@link Page}<{@link CmsOperationLogVo}>
     */
    Page<CmsOperationLogVo> findPageByContentId(Long contentId, Page page);

    /**
     * 同步内容管理操作日志
     *
     * @param contentId         内容id
     * @param title             标题
     * @param operationTypeEnum 操作日志类型枚举
     * @return {@link Long} 操作日志记录主键id
     */
    Long syncCmsOperationLog(Long contentId, String title, OperationTypeEnum operationTypeEnum);

}
