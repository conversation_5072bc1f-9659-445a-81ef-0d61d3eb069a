package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 反馈消息表
 * @TableName project_feedback_message
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_feedback_message")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "反馈消息表")
public class ProjectFeedbackMessage extends Model<ProjectFeedbackMessage> {
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 操作类型（0反馈提交、1确认、2取消、3已处理、4解决）
     */
    @ApiModelProperty(value = "操作类型")
    private Integer triggerType;

    /**
     * 反馈id
     */
    @ApiModelProperty(value = "反馈id")
    private Long feedbackId;

    /**
     * 接收人员
     */
    @ApiModelProperty(value = "接收人员")
    private String recipient;

    /**
     * 接收人员ID
     */
    @ApiModelProperty(value = "接收人员ID")
    private Long recipientId;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    /**
     * 禅道bug地址
     */
    @ApiModelProperty(value = "禅道bug地址")
    private String zentaoUrl;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;


}