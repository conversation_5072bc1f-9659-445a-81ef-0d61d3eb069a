package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 内容类别枚举类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Getter
public enum ContentCategoryEnum implements ValueEnum<Integer> {

    /**
     * 轮播图
     */
    CAROUSEL(0, "轮播图"),

    /**
     * 通知公告
     */
    NOTIFICATION(1, "通知公告"),

    /**
     * 公文
     */
    DOCUMENT(2, "公文"),

    /**
     * 培训计划
     */
    TRAINING(3, "培训计划"),

    /**
     * 新闻资讯
     */
    NEWS(4, "新闻资讯"),

    /**
     * 国科周报
     */
    WEEKLY(5, "国科周报"),

    /**
     * 解决方案
     */
    SOLUTION(6, "解决方案");




    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    ContentCategoryEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
