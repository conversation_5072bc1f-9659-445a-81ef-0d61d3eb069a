package com.gok.pboot.portal.dto;

import lombok.Data;

/**
 * 已处理Dto
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
public class WorkOrderProcessedDto {

    /**
     * 反馈ID
     */
    private Long feedbackId;

    /**
     * 跟进人员ID
     */
    private Long followedWorkerId;

    /**
     * 跟进人员姓名
     */
    private String followedWorker;

    /**
     * 紧急程度（0一般，1紧急，2紧急，3特别紧急）
     */
    private Integer urgencyDegree;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型
     */
    private Integer type;


    /**
     * 批转操作内容
     */
    private String operationContent;

}
