package com.gok.pboot.portal.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * CMS 发布类型枚举
 * CMS 发布类型枚举
 *
 * <AUTHOR>
 * @date 17/1/2024
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum CmsPublishTypeEnum {

    /**
     * 立刻
     */
    NOW(0,"立刻"),
    /**
     * 定时
     */
    REGULAR(1,"定时") ;

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;
}
