package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.DifyChatMessages;
import com.gok.pboot.portal.dto.ChatMessageDto;
import com.gok.pboot.portal.dto.ListConversationsDto;
import com.gok.pboot.portal.dto.ListMessagesDto;
import com.gok.pboot.portal.vo.CompletionsVo;
import com.gok.pboot.portal.vo.HistoryVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 国科小助手
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface IChatMessageService extends IService<DifyChatMessages> {

    /**
     * 新客服助手首次提示语
     *
     * @return 首次提示语
     */
    Map<String, Object> fastFirstTip();

    /**
     * 新客服助手聊天
     *
     * @param content 聊天内容
     * @return {@link R}<{@link CompletionsVo}>
     */
    R<CompletionsVo> talk(String content);

    /**
     * 获取聊天列表
     *
     * @param page 当前页
     * @param limit 每页条数
     * @param currentSize 当前页条数
     * @return {@link List}<{@link HistoryVo}>
     */
    List<HistoryVo> getHistoryList(Long page, Long limit, Integer currentSize);

    /**
     * dify首次提示语
     *
     * @return {@link Map}
     */
    Map<String, Object> firstTip();

    /**
     * 创建会话信息或基于此前的对话继续发送消息
     *
     * @param query 输入聊天内容
     * @return {@link R}<{@link ChatMessageDto}>
     */
    R<Object> chatMessages(String query);

    /**
     * 小助手聊天记录
     *
     * @param firstId 第一条聊天记录id
     * @param limit 条数
     * @return {@link R}<{@link ListMessagesDto}>
     */
    R<Object> listMessages(String firstId, Integer limit);


    /**
     * dify 删除掉rds的缓存key
     *
     * @param userId
     * @return
     */
    R delRdsKey(String userId);

    /**
     * 聊天会话列表（已弃用）
     *
     * @param lastId 最后一次聊天id
     * @param limit 条数
     * @return {@link R}<{@link ListConversationsDto}>
     */
    @Deprecated
    R<Object> listConversations(String lastId, Integer limit);

}
