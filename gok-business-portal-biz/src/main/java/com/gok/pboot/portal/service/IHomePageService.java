package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.portal.db.entity.PortalHrmResource;
import com.gok.pboot.portal.vo.ContentInfoVo;
import com.gok.pboot.portal.vo.RecommendedClassVo;
import com.gok.pboot.portal.vo.TrainingPlanVo;
import com.gok.pboot.portal.vo.UserInfoVo;

import java.util.List;

/**
 * <p>
 * 门户首页表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 **/
public interface IHomePageService {

    /**
     * 查询门户首页-个人信息
     *
     * @return
     */
    UserInfoVo getUserInfo();

    /**
     * 查询门户首页-培训文件
     *
     * @param page
     * @return
     */
    Page<TrainingPlanVo> getTrainingPlan(Page page);

    /**
     * 查询门户首页-内容查看
     *
     * @param contentId
     * @return
     */
    ContentInfoVo getContent(Long contentId);

    /**
     * 查询门户首页-推荐课程
     *
     * @param phone
     * @return
     */
    List<RecommendedClassVo> getRecommendedClass(String phone);

    /**
     * 获取当前登录用户花名册信息
     *
     * @return 用户花名册信息
     */
    PortalHrmResource getCurrentUser();

}
