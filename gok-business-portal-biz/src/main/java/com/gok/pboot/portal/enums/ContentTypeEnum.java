package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 内容类型枚举类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Getter
public enum ContentTypeEnum implements ValueEnum<Integer> {

    /**
     * 图文
     */
    IMAGE_TEXT(0, "图文"),

    /**
     * H5链接
     */
    H5_LINK(1, "H5链接"),

    /**
     * 视频
     */
    VIDEO(2, "视频");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    ContentTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 判断字典id是否合法
     *
     * @param value 字典id
     * @return 合法-true 非法-false 空-false
     */
    public static boolean isValueLegal(Integer value) {
        if (!Optional.ofNullable(value).isPresent()) {
            return false;
        }
        Optional<OperationTypeEnum> optional = Arrays.asList(OperationTypeEnum.values()).stream()
                .filter(e -> value.equals(e.getValue()))
                .findAny();
        return optional.isPresent();
    }

}
