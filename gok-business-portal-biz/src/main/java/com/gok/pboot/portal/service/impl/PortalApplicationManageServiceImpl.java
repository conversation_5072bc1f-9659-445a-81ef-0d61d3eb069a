package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysOauthClientDetailsVO;
import com.gok.pboot.portal.db.entity.PortalApplicationManage;
import com.gok.pboot.portal.db.entity.PortalSysFile;
import com.gok.pboot.portal.db.mapper.PortalApplicationManageMapper;

import com.gok.pboot.portal.db.mapper.PortalSysFileMapper;
import com.gok.pboot.portal.dto.PortalApplicationManagePageDto;
import com.gok.pboot.portal.dto.PortalApplicationManageSaveOrUpdateDto;
import com.gok.pboot.portal.service.IPortalApplicationManageService;


import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.vo.PortalApplicationManageBoxVo;
import com.gok.pboot.portal.vo.PortalApplicationManageVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.swing.*;
import java.time.LocalDateTime;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PortalApplicationManageServiceImpl extends ServiceImpl<PortalApplicationManageMapper, PortalApplicationManage> implements IPortalApplicationManageService {


    @Autowired
    private PortalApplicationManageMapper portalApplicationManageMapper;

    @Autowired
    private PortalSysFileMapper portalSysFileMapper;

    @Resource
    private RemoteOutService remoteOutService;

    @Resource
    private CdnImgUtils cdnImgUtils;

    @Override
    public Integer queryByCategoryId(Long id) {

        LambdaQueryWrapper<PortalApplicationManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PortalApplicationManage::getCategoryId, id);

        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public R<Page<PortalApplicationManageVo>> queryAllManage(Page page, PortalApplicationManagePageDto pageDto) {
        LambdaQueryWrapper<PortalApplicationManage> queryWrapper = new LambdaQueryWrapper<>();
        //排除已经被逻辑删除的
        queryWrapper.eq(PortalApplicationManage::getDelFlag, 0);

        //模糊查询
        if (ObjectUtil.isNotEmpty(pageDto.getApplicationName())) {
            //应用名称
            queryWrapper.like(PortalApplicationManage::getApplicationName, pageDto.getApplicationName());
        }
        if (ObjectUtil.isNotEmpty(pageDto.getStatus())) {
            //应用状态
            queryWrapper.eq(PortalApplicationManage::getStatus, pageDto.getStatus());
        }
        if (ObjectUtil.isNotEmpty(pageDto.getCategoryId())) {
            //应用类型
            queryWrapper.eq(PortalApplicationManage::getCategoryId, pageDto.getCategoryId());
        }
        //设置排序
        queryWrapper.orderByDesc(PortalApplicationManage::getWeight).orderByDesc(PortalApplicationManage::getLunchTime);

        Page<PortalApplicationManage> pageInfo = new Page<>(page.getCurrent(), page.getSize());
        //获取实体类分页
        this.page(pageInfo, queryWrapper);

        //拷贝实体类分页到VO类的分页
        List<PortalApplicationManage> records = pageInfo.getRecords();
        List<PortalApplicationManageVo> collect = records.stream().map(e -> {
            PortalApplicationManageVo vo = new PortalApplicationManageVo();
            BeanUtil.copyProperties(e, vo);
            vo.setFileUrl(cdnImgUtils.createHttpUrl(vo.getFileUrl()));
            return vo;
        }).collect(Collectors.toList());

        Page<PortalApplicationManageVo> voPage = new Page<>(page.getCurrent(), page.getSize());
        BeanUtil.copyProperties(pageInfo, voPage);
        voPage.setRecords(collect);

        return R.ok(voPage);
    }

    @Override
    public R onOrOff(Long id) {
        //找到数据
        PortalApplicationManage applicationManage = this.getById(id);
        if (null == applicationManage) {
            return R.ok("操作失败");
        }
        //未上架->上架
        if (applicationManage.getStatus() == 1) {
            applicationManage.setStatus(0);
            applicationManage.setLunchTime(LocalDateTime.now());
        } else {
            //上架->下架
            applicationManage.setStatus(1);
            applicationManage.setLunchTime(null);
        }
        //查询封装
        applicationManage.setUpdateTime(LocalDateTime.now());
        applicationManage.setUpdateBy(UserUtils.getUser().getName());
        //修改上下架
        this.updateById(applicationManage);

        return R.ok("操作成功");
    }

    @Override
    public R saveDto(PortalApplicationManageSaveOrUpdateDto dto) {
        dto.setId(null);
        if (dto.getApplicationName() == null || dto.getBelongApplicationId() == null || dto.getCategoryId() == null) {
            return R.failed("信息填写不全,新增失败");
        }

        PortalApplicationManage portalApplicationManage = new PortalApplicationManage();
        BeanUtil.copyProperties(dto, portalApplicationManage);
        portalApplicationManage.setCreateBy(UserUtils.getUser().getName());
        portalApplicationManage.setCreateTime(LocalDateTime.now());
        if (dto.getStatus() == 0) {
            portalApplicationManage.setLunchTime(LocalDateTime.now());
        }
        portalApplicationManageMapper.insertReturnId(portalApplicationManage);
        return R.ok(portalApplicationManage.getId(), "新增成功");
    }

    @Override
    public R updateDto(PortalApplicationManageSaveOrUpdateDto dto) {
        if (dto.getApplicationName() == null || dto.getBelongApplicationId() == null || dto.getCategoryId() == null) {
            return R.failed("信息填写不全,修改失败");
        }

        //获取旧的的信息
        PortalApplicationManage oldManage = this.getById(dto.getId());
        PortalApplicationManage newManage = new PortalApplicationManage();
        if (oldManage == null) {
            return R.failed("该应用不存在");
        }
        //复制基础信息
        BeanUtil.copyProperties(dto, newManage);
        newManage.setLunchTime(oldManage.getLunchTime());
        if (oldManage.getStatus() == 1 && dto.getStatus() == 0) {
            newManage.setLunchTime(LocalDateTime.now());
        }
        //添加修改时间修改人
        newManage.setUpdateBy(UserUtils.getUser().getName());
        newManage.setUpdateTime(LocalDateTime.now());
        this.updateById(newManage);
        return R.ok("修改成功");
    }

    @Override
    public R remove(Long id) {
        PortalApplicationManage manage = this.getById(id);
        if (null == manage) {
            return R.failed("应用不存在");
        }
        if (manage.getStatus() == 0) {
            return R.failed("应用未下架");
        }
        manage.setDelFlag("1");
        this.updateById(manage);
        return R.ok("删除成功");
    }

    @Override
    public List<PortalApplicationManageBoxVo> offShelfList() {
        R<List<SysOauthClientDetailsVO>> appListByUserId = remoteOutService.getAppList();
        List<SysOauthClientDetailsVO> rightApp = appListByUserId.getData();

        //1.数据库查找未上架的应用
        List<PortalApplicationManageBoxVo> applicationIconVos = portalApplicationManageMapper.queryOffShelf();

        //2.类型转换
        List<PortalApplicationManageBoxVo> res = new ArrayList<>();
        for (SysOauthClientDetailsVO rightAppVo : rightApp) {
            if (rightAppVo != null && "0".equals(rightAppVo.getStatus())) {
                PortalApplicationManageBoxVo vo = new PortalApplicationManageBoxVo();
                vo.setId(rightAppVo.getId());
                vo.setApplicationName(rightAppVo.getName());
                vo.setLinkAddress(rightAppVo.getRedirectUrl());

                res.add(vo);
            }
        }
        //3.取除重复的数据
        res.removeAll(applicationIconVos);

        return res;
    }

    @Override
    public PortalApplicationManageVo getOne(Long id) {
        PortalApplicationManage manage = this.getById(id);
        if (manage == null) {
            return null;
        }
        PortalSysFile portalSysFile = portalSysFileMapper.selectById(manage.getFileId());
        PortalApplicationManageVo vo = new PortalApplicationManageVo();
        BeanUtil.copyProperties(manage, vo);
        if (portalSysFile != null) {
            vo.setFileUrl(cdnImgUtils.createHttpUrl(portalSysFile.getFileUrl()));
        }

        return vo;
    }

}
