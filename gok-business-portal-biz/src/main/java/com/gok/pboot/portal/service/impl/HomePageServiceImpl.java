package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysUserInfoVo;
import com.gok.bcp.upms.vo.SysUserMhVo;
import com.gok.components.common.user.UserUtils;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysDeptVo;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.*;
import com.gok.pboot.portal.db.mapper.*;
import com.gok.pboot.portal.enums.BusinessTypeEnum;
import com.gok.pboot.portal.enums.ContentTypeEnum;
import com.gok.pboot.portal.enums.LaunchStatusEnum;
import com.gok.pboot.portal.service.IHomePageService;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.util.EnumUtils;
import com.gok.pboot.portal.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 门户首页表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class HomePageServiceImpl implements IHomePageService {

    @Resource
    private PortalHrmresourceMapper portalHrmresourceMapper;

    @Resource
    private CmsContentMapper cmsContentMapper;

    @Resource
    private CmsContentFileMapper cmsContentFileMapper;

    @Resource
    private PortalSysFileMapper portalSysFileMapper;

    @Resource
    private SysDictItemMapper sysDictItemMapper;

    @Resource
    private CdnImgUtils cdnImgUtils;

    @Value("${teachUrl}")
    private String url;

    @Value("${gok.tenantId}")
    private Long gokTenantId;

    @Value("${gok.edu.url}")
    private String gokEduUrl;

    @Value("${gok.edu.appId}")
    private Long gokEduAppId;

    @Resource
    private RemoteOutService remoteOutService;

    String CDN_URL = "https://gok-bcp-prod.obs.cn-east-2.myhuaweicloud.com/";

    /**
     * 职级字典
     */
    private static final String POSITION_LEVEL = "position-level";

    /**
     * 查询门户首页-个人信息
     *
     * @return
     */
    @Override
    public UserInfoVo getUserInfo() {
        UserInfoVo userInfoVo = new UserInfoVo();
        //查询中台个人信息
        //获取用户id
        Long id = UserUtils.getUser().getId();
        SysUserInfoVo sysUserVo = remoteOutService.getUserInfoMhById(UserUtils.getUser().getId()).getData();
        userInfoVo.setId(id);
        if (!Optional.ofNullable(sysUserVo).isPresent()) {
            return new UserInfoVo();
        }
            SysUserMhVo sysUser = sysUserVo.getSysUser();
            userInfoVo.setPhone(sysUser.getPhone());
            userInfoVo.setAvatar(sysUser.getAvatar());
            userInfoVo.setUsrName(sysUser.getUsername());
            userInfoVo.setWorkCode(sysUser.getStaffId());
            userInfoVo.setJob(sysUser.getMemberPosition());
            //获取用户组织信息
            userInfoVo.setDeptName(sysUser.getDeptName());
            userInfoVo.setAliasName(sysUser.getName());

        //查询花名册个人信息
        UserInfoVo vo = portalHrmresourceMapper.selByMobile(sysUser.getPhone());
        //数据封装
        if (Optional.ofNullable(vo).isPresent()) {
            userInfoVo.setBirthday(vo.getBirthday());
            userInfoVo.setStartDate(vo.getStartDate());
            userInfoVo.setJobDays(vo.getJobDays());
            userInfoVo.setPositionLevel(vo.getPositionLevel());
            //查询职级字典值
            List<SysDictItem> dictItemList = sysDictItemMapper.selByType(POSITION_LEVEL);
            Map<String, String> labelMap = dictItemList.stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel, (a, b) -> a));
            userInfoVo.setPositionLevelTxt(labelMap.getOrDefault(vo.getPositionLevel(), StrUtil.EMPTY));
        }

        return userInfoVo;
    }

    /**
     * 查询门户首页-培训文件
     *
     * @param page
     * @return
     */
    @Override
    public Page<TrainingPlanVo> getTrainingPlan(Page page) {
        //查询培训文件列表
        Page<CmsContent> cmsContentPage = cmsContentMapper.findTrainingPlan(page);
        List<CmsContent> cmsContentList = cmsContentPage.getRecords();
        Page<TrainingPlanVo> voPage = new Page<>();
        BeanUtil.copyProperties(cmsContentPage, voPage, "records");
        if (CollUtil.isEmpty(cmsContentList)) {
            return voPage;
        }
        //数据封装
        List<TrainingPlanVo> trainingPlanVoList = cmsContentList.stream().map(e -> {
            TrainingPlanVo vo = BeanUtil.copyProperties(e, TrainingPlanVo.class);
            vo.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, e.getStatus()));
            return vo;
        }).collect(Collectors.toList());
        voPage.setRecords(trainingPlanVoList);
        return voPage;
    }

    /**
     * 查询门户首页-内容查看
     *
     * @param contentId
     * @return
     */
    @Override
    public ContentInfoVo getContent(Long contentId) {
        //获取内容信息
        CmsContent cmsContent = cmsContentMapper.selectById(contentId);
        //如果内容不存在
        Assert.isTrue(Optional.ofNullable(cmsContent).isPresent(), "内容不存在");
        //拷贝
        ContentInfoVo contentInfoVo = BeanUtil.copyProperties(cmsContent, ContentInfoVo.class);
        //数据组装
        contentInfoVo.setBusinessTypeTxt(EnumUtils.getNameByValue(BusinessTypeEnum.class, cmsContent.getBusinessType()));
        contentInfoVo.setContentTypeTxt(EnumUtils.getNameByValue(ContentTypeEnum.class, cmsContent.getContentType()));
        contentInfoVo.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, cmsContent.getStatus()));
        //查询内容附件信息
        List<CmsContentFile> fileList = cmsContentFileMapper.selByContentId(contentId);
        List<ContentFileVo> contentFileVos = new ArrayList<>();
        BeanUtil.copyProperties(fileList, contentFileVos);
        if (CollUtil.isNotEmpty(contentFileVos)) {
            //获取文件id集合
            List<Long> fileIds = fileList.stream().map(CmsContentFile::getFileId).collect(Collectors.toList());
            //根据文件ids查询文件列表
            List<PortalSysFile> portalSysFileList = portalSysFileMapper.selByIds(fileIds);

            if (portalSysFileList!=null&&!portalSysFileList.isEmpty()){
                for (PortalSysFile portalSysFile:portalSysFileList){
                    portalSysFile.setFileUrl(cdnImgUtils.createHttpUrl(portalSysFile.getFileUrl()));
                }
            }


            //key:fileId,value:fileSize
            Map<Long, Long> fileSizeMap = portalSysFileList.stream().collect(Collectors.toMap(PortalSysFile::getId, PortalSysFile::getFileSize, (a, b) -> a));
            //数据组装-文件大小
            BeanUtil.copyProperties(fileList, contentFileVos);
            contentFileVos.stream().map(e -> {
                e.setFileSize(fileSizeMap.getOrDefault(e.getFileId(), NumberUtils.LONG_ZERO));
                return e;
            }).collect(Collectors.toList());
        }
        contentInfoVo.setContentFileVoList(contentFileVos);
        return contentInfoVo;
    }


    @Override
    public List<RecommendedClassVo> getRecommendedClass(String phone) {
        //构建访问的url
        String requestUrl = url + "?phone=" + phone + "&exclusiveTenantId=" + gokTenantId;
        //请求
        String returnStr = HttpUtil.get(requestUrl, CharsetUtil.CHARSET_UTF_8);
        //将返回数据转换成jsonArray
        JSONArray jsonArray = JSONUtil.parseArray(returnStr);
        //数据组装
        List<RecommendedClassVo> list = JSONUtil.toList(jsonArray, RecommendedClassVo.class);


        //封装TenantId
        list.stream().map(e -> {
            e.setTenantId(gokTenantId);
            e.setLinkAddress(gokEduUrl);
            e.setBelongApplicationId(gokEduAppId);
            return e;
        }).collect(Collectors.toList());
        return list;
    }

    @Override
    public PortalHrmResource getCurrentUser() {
        // 根据手机号获取对应用户信息
        String phone = SecurityUtils.getUser().getPhone();
        log.info("user phone is:{}", phone);
        List<PortalHrmResource> portalHrmResource = portalHrmresourceMapper.selByPhone(phone);
        if (CollUtil.isEmpty(portalHrmResource)) {
            return null;
        }
        return portalHrmResource.stream().findAny().get();
    }

}
