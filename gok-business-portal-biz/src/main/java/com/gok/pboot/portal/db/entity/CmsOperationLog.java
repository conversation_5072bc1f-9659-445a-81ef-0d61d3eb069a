package com.gok.pboot.portal.db.entity;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gok.pboot.portal.dto.CmsOperationLogSaveDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 内容操作记录
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cms_operation_log")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "内容操作记录")
public class CmsOperationLog extends Model<CmsOperationLog> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 内容id
     */
    @ApiModelProperty(value = "内容id")
    private Long contentId;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 操作类型（0创建、1编辑、2删除）
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operationType;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String operationContent;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

    /**
     * 构建保存
     *
     * @param request 请求
     * @return {@link CmsOperationLog}
     */
    public static CmsOperationLog buildSave(CmsOperationLogSaveDto request) {

        return BeanUtil.copyProperties(request, CmsOperationLog.class);
    }

}
