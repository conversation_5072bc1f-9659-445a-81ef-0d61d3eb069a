package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 内容管理
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cms_content")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "内容管理")
public class CmsContent extends Model<CmsContent> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 业务分类
     * {@link com.gok.pboot.portal.enums.BusinessTypeEnum}
     */
    @ApiModelProperty(value = "业务分类")
    private Integer businessType;

    /**
     * 内容类型（0图文、1H5链接、2视频）
     * {@link com.gok.pboot.portal.enums.ContentTypeEnum}
     */
    @ApiModelProperty(value = "内容类型（0图文、1H5链接、2视频）")
    private Integer contentType;

    /**
     * 内容类别
     * {@link com.gok.pboot.portal.enums.ContentCategoryEnum}
     */
    @ApiModelProperty(value = "内容类别（0轮播图、1通知公告、2公文、3培训计划、4新闻资讯、5国科周报）")
    private Integer contentCategory;

    /**
     * 发布者id
     */
    @ApiModelProperty(value = "发布者id")
    private Long publisherId;

    /**
     * 发布者名称
     */
    @ApiModelProperty(value = "发布者名称")
    private String publisherName;

    /**
     * 定时发布时间
     */
    @ApiModelProperty(value = "定时发布时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime publishTime;

    /**
     * 上架状态（0已上架、1未上架）
     * {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     */
    @ApiModelProperty(value = "上架状态（0已上架、1未上架）")
    private Integer status;

    /**
     * 正文
     */
    @ApiModelProperty(value = "正文")
    private String contentText;

    /**
     * 关联内容id
     */
    @ApiModelProperty(value = "关联内容id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long relationContentId;

    /**
     * 链接地址
     */
    @ApiModelProperty(value = "链接地址")
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String linkAddress;

    /**
     * 封面图url
     */
    @ApiModelProperty(value = "封面图url")
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String coverImageUrl;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建者名称
     */
    @ApiModelProperty(value = "创建者名称")
    private String createUserName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 上架时间
     */
    @ApiModelProperty(value = "上架时间")
    private LocalDateTime launchTime;

    /**
     * delFlag
     */
    @TableLogic
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

    /**
     * 展示状态（0展示、1不展示）
     * {@link com.gok.pboot.portal.enums.ShowFlagEnum}
     */
    @ApiModelProperty(value = "展示状态")
    private Integer showFlag;

    /**
     * 是否发送
     * {@link com.gok.pboot.portal.enums.CmsSendEnum}
     */
    @ApiModelProperty(value = "是否发送")
    private Integer isSend;

    /**
     * 发送对象
     * {@link com.gok.pboot.portal.enums.CmsSendObjEnum}
     */
    @ApiModelProperty(value = "发送对象")
    private Integer sendObj;

    /**
     * 发布类型
     * {@link com.gok.pboot.portal.enums.CmsPublishTypeEnum}
     */
    @ApiModelProperty(value = "发布类型")
    private Integer publishType;



}
