package com.gok.pboot.portal.enums;


import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 反馈类型枚举类
 *
 * <AUTHOR>
 * @since 2023-08-24
 */

@Getter
public enum FeedbackType implements ValueEnum<Integer> {

    /**
     * 0 优化建议
     */
    OPTIMIZE(0, "优化建议"),

    /**
     * 1 功能异常
     */
    DYSFUNCTION(1, "功能异常"),

    /**
     * 2 其他
     */
    OTHER(2, "其他");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FeedbackType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按值获取名称
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String getNameByValue(Integer value){
        if (value == null){
            return null;
        }
        for (FeedbackType feedbackType : FeedbackType.values()) {
            if (feedbackType.getValue().equals(value)) {
                return feedbackType.getName();
            }
        }
    return null;
    }
}
