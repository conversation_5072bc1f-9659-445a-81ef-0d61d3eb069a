package com.gok.pboot.portal.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.components.common.util.R;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.portal.dto.*;
import com.gok.pboot.portal.dto.*;
import com.gok.pboot.portal.dto.PortalApplicationManageSaveOrUpdateDto;
import com.gok.pboot.portal.dto.ProjectFeedbackPageDto;
import com.gok.pboot.portal.dto.ProjectFeedbackSaveDto;
import com.gok.pboot.portal.dto.WorkOrderDetailUpdateDto;
import com.gok.pboot.portal.service.IProjectFeedbackService;
import com.gok.pboot.portal.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 项目反馈控制器
 *
 * <AUTHOR>
 * @date 2023/08/24
 * @menu 反馈内容
 * @menu ProjectFeedbackController
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/projectFeedBack")
@Api(tags = "项目反馈")
public class ProjectFeedbackController {

    @Autowired
    private final IProjectFeedbackService service;


    /**
     * 列表反馈
     * 分页反馈列表，自己的提交的
     *
     * @param page    页面
     * @param content 内容
     * @return R
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页查询")
    public R<Page<ProjectFeedbackVo>> listFeedback(Page page, String content) {
        return service.queryFeedbackByCreate(page, content);
    }

    /**
     * 取消反馈
     *
     * @param id id
     * @return {@link R}
     */
    @PutMapping("/cancel/{id}")
    public R cancel(@PathVariable Long id) {
        return service.removeProjectFeedback(id);
    }

    /**
     * 关闭反馈
     *
     * @param id id
     * @return {@link R}
     */
    @PutMapping("/close/{id}")
    public R close(@PathVariable Long id) {
        return service.closeProjectFeedback(id);
    }

    /**
     * 新增反馈
     *
     * @param dto dto
     * @return {@link R}
     */
    @PostMapping("/save")
    public R save(@Valid @RequestBody ProjectFeedbackSaveDto dto) {
        return service.saveDto(dto);
    }


    /**
     * 得到反馈详情
     *
     * @param id id
     * @return R
     */
    @GetMapping("/getDetail/{id}")
    public R<ProjectFeedbackDetailVo> getDetail(@PathVariable Long id) {
        return service.getDetail(id);
    }

    /**
     * 满意度
     *
     * @param projectFeedbackSatisfactionDto 项目反馈满意度 DTO
     * @return {@link R}
     */
    @PostMapping("/satisfaction")
    public R satisfaction(@RequestBody ProjectFeedbackSatisfactionDto projectFeedbackSatisfactionDto) {
        return service.satisfaction(projectFeedbackSatisfactionDto);
    }

    /**
     * 工单管理--反馈列表接口
     *
     * @param page          页面
     * @param workOrderPage 工作订单页面
     * @return R
     */
    @GetMapping("/workOrderPage")
    public R<Page<WorkOrderPageVo>> workOrderPage(Page page, WorkOrderPageDto workOrderPage) {
        return service.workOrderPage(page, workOrderPage);
    }


    /**
     * 工单管理--详情界面修改跟进人
     *
     * @param dto 修改dto
     * @return R
     */
    @PostMapping("/workOrderDetailUpdate")
    public R workOrderDetailUpdate(@Valid @RequestBody WorkOrderDetailUpdateDto dto) {
        return service.workOrderDetailUpdate(dto);
    }


    /**
     * 姓名模糊查询
     *
     * @param userName 模糊姓名
     * @return 姓名列表
     */
    @GetMapping("/userLikeList")
    public R<List<SysUserOutVO>> userLikeList(@RequestParam String userName) {
        return service.userLikeList(userName);
    }


    @Inner
    @PostMapping("get/unclosedBugs")
    @ApiOperation(value = "处理获取禅道关闭任务", notes = "处理获取禅道关闭的任务")
    public List<UnclosedBugsVo> getUnclosedBugs() {
        return service.getUnclosedBugs();
    }

    /**
     * 显示工单
     * 四个界面显示接口
     *
     * @param id 编号
     * @return {@link R}<{@link WorkOrderVo}>
     */
    @GetMapping("/show/{id}")
    public R<WorkOrderVo> showWorkOrder(@PathVariable Long id) {
        return service.showWorkOrder(id);
    }

    /**
     * 四个界面的记录
     *
     * @param workOrderDto 工作订单 DTO
     * @return {@link R}
     */
    @PostMapping("/doWorkOrder")
    public R doWorkOrder(@Valid @RequestBody WorkOrderDto workOrderDto) {
        return service.doWorkOrder(workOrderDto);
    }

    /**
     * 前台反馈应用下拉框
     *
     * @return R
     */
    @GetMapping("/appBox")
    @ApiOperation(value = "前台反馈应用下拉框")
    public R<List<ApplicationIconVo>> getAppBox() {
        return R.ok(service.getAppBox());
    }
}
