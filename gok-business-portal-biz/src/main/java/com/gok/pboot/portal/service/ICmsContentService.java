package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.portal.db.entity.CmsContent;
import com.gok.pboot.portal.dto.CmsContentDto;
import com.gok.pboot.portal.dto.CmsContentSaveOrUpdateDto;
import com.gok.pboot.portal.vo.*;

import java.util.List;

/**
 * <p>
 * 内容管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-08
 **/
public interface ICmsContentService extends IService<CmsContent> {

    /**
     * 分页查询内容列表
     *
     * @param page    分页请求
     * @param request 查询请求
     * @return 分页查询结果
     */
    Page<CmsContentVo> findPage(Page page, CmsContentDto request);

    /**
     * 查询内容详情
     *
     * @param contentId 内容id
     * @param isAdmin   是管理员
     * @return {@link CmsContentDetailVo}
     */
    CmsContentDetailVo findDetail(Long contentId,boolean isAdmin);


    /**
     * 内容管理-新增内容
     * 根据内容类别字段区分具体实现逻辑
     *
     * @param request 新增请求
     * @return 新增记录主键id
     */
    Long save(CmsContentSaveOrUpdateDto request);

    /**
     * 内容管理-编辑内容
     * 根据内容类别字段区分具体实现逻辑
     *
     * @param request 新增请求
     * @return 编辑记录主键id
     */
    Long update(CmsContentSaveOrUpdateDto request);

    /**
     * 修改内容上下架状态
     *
     * @param id     编辑记录主键id
     * @param status 上下架状态
     * @return 编辑记录主键id
     */
    Long updateStatus(Long id, Integer status);

    /**
     * 根据id集合批量删除
     *
     * @param ids IDS
     * @return {@link List}<{@link Long}>
     * @Param: ids 内容id集合[]
     * @return: 内容id集合[]
     */
    List<Long> batchDelete(List<Long> ids);

    /**
     * 按发布时间发送消息
     *
     * @return {@link List}<{@link CmsContentPublishVo}>
     */
    List<CmsContentPublishVo> sendMsgByPublishTime();

    /**
     * 查找公文分类计数
     *
     * @return {@link CmsDocCountVo}
     */
    CmsDocCountVo findSortCount();
}
