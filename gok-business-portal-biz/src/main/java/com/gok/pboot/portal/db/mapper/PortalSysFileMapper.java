package com.gok.pboot.portal.db.mapper;

import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.PortalSysFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 文件表 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Mapper
public interface PortalSysFileMapper extends BaseMapper<PortalSysFile> {

    /**
     * 根据文件id集合查询文件信息
     * @param fileIds
     * @return
     */
    List<PortalSysFile> selByIds(@Param("fileIds") List<Long> fileIds);
}