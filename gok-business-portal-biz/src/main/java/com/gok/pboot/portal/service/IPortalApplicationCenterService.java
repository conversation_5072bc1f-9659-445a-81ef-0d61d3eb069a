package com.gok.pboot.portal.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalApplicationCenter;
import com.gok.pboot.portal.dto.PortalApplicationCenterDto;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.ApplicationViewVo;

import java.util.List;

/**
 * <p>
 * 应用中心表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
public interface IPortalApplicationCenterService extends IService<PortalApplicationCenter> {

    /**
     * 编辑常用应用列表
     *
     * @param dtoList 常用应用列表
     * @return R
     */
    R edit(List<PortalApplicationCenterDto> dtoList);

    /**
     * 返回界面全部应用
     *
     * @param isAll 是否获取全部
     * @return {@link List}<{@link ApplicationViewVo}>
     */
    List<ApplicationViewVo> listApplication(Boolean isAll);

    /**
     * 获取前18个常用图标
     *
     * @return
     */
    List<ApplicationIconVo> usefulList();

    /**
     * 搜索框模糊查询
     *
     * @param content 查询内容
     * @return 应用列表
     */
    List<ApplicationIconVo> queryAppByLike(String content);
}
