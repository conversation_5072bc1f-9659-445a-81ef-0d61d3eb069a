package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 反馈-操作类型枚举类-反馈消息表实用
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Getter
public enum FeedbackTriggerTypeEnum implements ValueEnum<Integer> {

    /**
     * 0 反馈提交
     */
    SUBMIT(0, "反馈提交"),

    /**
     * 1 确认
     */
    CONFIRM(1, "确认"),

    /**
     * 2 取消
     */
    CANCEL(2, "取消"),
    /**
     * 3 已处理
     */
    PROCESSED(3, "已处理"),

    /**
     * 4 解决
     */
    SOLVE(4, "解决"),

    /**
     * 5 搁置
     */
    SHELVE(5,"搁置");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FeedbackTriggerTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按值获取名称
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String getNameByValue(Integer value){
        if (value == null){
            return null;
        }
        for (FeedbackTriggerTypeEnum feedbackTriggerTypeEnum : FeedbackTriggerTypeEnum.values()) {
            if (feedbackTriggerTypeEnum.getValue().equals(value)) {
                return feedbackTriggerTypeEnum.getName();
            }
        }
        return null;
    }
}
