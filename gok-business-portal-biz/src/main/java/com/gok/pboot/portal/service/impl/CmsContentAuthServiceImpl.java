package com.gok.pboot.portal.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.SysDept;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.CmsContentAuth;
import com.gok.pboot.portal.enums.CmsSendObjEnum;
import com.gok.pboot.portal.service.CmsContentAuthService;
import com.gok.pboot.portal.db.mapper.CmsContentAuthMapper;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CMS内容认证服务实现
 *
 * <AUTHOR>
 * @description 针对表【cms_content_auth(内容权限表)】的数据库操作Service实现
 * @createDate 2024-01-12 09:20:36
 * @date 2024/01/12
 */
@Service
public class CmsContentAuthServiceImpl extends ServiceImpl<CmsContentAuthMapper, CmsContentAuth>
    implements CmsContentAuthService{


    @Override
    public void batchSaveByObj(Integer obj,Long contentId, List<Long> idList) {
        //不需要批量修改
        if (CmsSendObjEnum.ALL.getValue().equals(obj) || CollUtil.isEmpty(idList)) {
            return;
        }

        //根据部门id修改
        if (CmsSendObjEnum.DEPT.getValue().equals(obj)) {
            baseMapper.batchSaveByDeptId(contentId,idList, SecurityUtils.getUser().getUsername());
        }

        //根据用户id修改
        if (CmsSendObjEnum.USER.getValue().equals(obj)) {
            baseMapper.batchSaveByUserId(contentId,idList, SecurityUtils.getUser().getUsername());
        }


    }

    @Override
    public void batchDeleteByContentId(Long contentId) {
        baseMapper.batchDeleteByContentId(contentId);
    }

    @Override
    public void batchDeleteByContentIdList(List<Long> contentIdList) {
        baseMapper.batchDeleteByContentIdList(contentIdList);

    }

    @Override
    public List<Long> getIdListByContentId(Integer sendObj, Long contentId) {


        //根据部门id
        if (CmsSendObjEnum.DEPT.getValue().equals(sendObj)) {
            return baseMapper.getDeptIdListByContentId(contentId);
        }

        //根据用户id
        if (CmsSendObjEnum.USER.getValue().equals(sendObj)) {
            return baseMapper.getUserIdListByContentId(contentId);
        }

        return new ArrayList<>();
    }

    @Override
    public Boolean isAuthByCmsId( Long contentId, Integer sendObj) {

        if (CmsSendObjEnum.ALL.getValue().equals(sendObj)) {
            return true;
        }

        Integer count = null;

        if (CmsSendObjEnum.DEPT.getValue().equals(sendObj)) {
            count = baseMapper.isAuthByDeptId(contentId,SecurityUtils.getUser().getDeptList().stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        }else if (CmsSendObjEnum.USER.getValue().equals(sendObj)){
            count =  baseMapper.isAuthByUserId(contentId,SecurityUtils.getUser().getId());
        }

        return count != null && !NumberUtils.INTEGER_ZERO.equals(count);
    }
}




