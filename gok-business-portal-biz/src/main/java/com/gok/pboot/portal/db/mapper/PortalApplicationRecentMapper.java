package com.gok.pboot.portal.db.mapper;

import com.gok.pboot.portal.db.entity.PortalApplicationRecent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【portal_application_recent(应用中心-最近应用)】的数据库操作Mapper
* @createDate 2023-10-25 11:22:48
* @Entity com.gok.pboot.portal.db.entity.PortalApplicationRecent
*/
public interface PortalApplicationRecentMapper extends BaseMapper<PortalApplicationRecent> {


    /**
     * 最近应用列表
     *
     * @param userId 用户标识
     * @return {@link List}<{@link ApplicationIconVo}>
     */
    List<ApplicationIconVo> recentList(Long userId);

    /**
     * 按用户应用 ID 计数
     *
     * @param userId        用户 ID
     * @param applicationId 应用程序 ID
     * @return {@link Integer}
     */
    Integer countByUserAppId(@Param("userId") Long userId,@Param("applicationId") Long applicationId);


    /**
     * 更新最近应用
     *
     * @param userId        用户 ID
     * @param applicationId 应用程序 ID
     */
    void updateRecent(@Param("userId") Long userId,@Param("applicationId") Long applicationId);
}




