package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.flowable.task.dto.task.TaskStatusSyncDto;
import com.gok.bcp.flowable.task.vo.TaskDetailVo;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.WorkFlowPageDTO;
import com.gok.pboot.portal.vo.WorkFlowTaskVo;
import com.gok.pboot.portal.vo.WorkflowTaskTotal;

/**
 * 任务服务层
 *
 * <AUTHOR>
 * @date 27/10/2023
 */
public interface IWorkFlowTaskService {

    /**
     * 获取远程任务页面
     *
     * @param workFlowPageDTO {@link WorkFlowPageDTO} 分页参数
     * @return {@link R}<{@link Page}<{@link WorkFlowTaskVo}>>
     */
    R<Page<WorkFlowTaskVo>> getRemotePage(WorkFlowPageDTO workFlowPageDTO);

    /**
     * 任务详情
     *
     * @param id 编号
     * @return {@link R}<{@link TaskDetailVo}>
     */
    R<TaskDetailVo> getTaskById(Long id);

    /**
     * 按 ID 获取任务跳转链接
     *
     * @param id {@link Long} 编号
     * @return {@link R}<{@link String}>
     */
    R<String> getWorkflowById(Long id);

    /**
     * 状态同步
     *
     * @param taskStatusSyncDto {@link TaskStatusSyncDto} 任务状态同步
     * @return {@link R}<{@link Boolean}>
     */
    R<Boolean> statusSync(TaskStatusSyncDto taskStatusSyncDto);

    /**
     * 获取任务状态总计
     *
     * @return {@link R}<{@link WorkflowTaskTotal>
     */
    R<WorkflowTaskTotal> total();

}
