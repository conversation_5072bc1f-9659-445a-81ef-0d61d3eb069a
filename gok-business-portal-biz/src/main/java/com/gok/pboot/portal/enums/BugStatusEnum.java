package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * BUG状态
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Getter
public enum BugStatusEnum implements ValueEnum<String> {

    /**
     * 激活
     */
    ACTIVE("active", "激活"),

    RESOLVED("resolved", "已解决"),

    CLOSED("closed", "已关闭");

    private String value;

    private String name;

    BugStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
