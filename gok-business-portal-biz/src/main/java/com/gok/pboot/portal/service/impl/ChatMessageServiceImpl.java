package com.gok.pboot.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.constant.RedisConstant;
import com.gok.pboot.portal.db.entity.DifyChatMessages;
import com.gok.pboot.portal.db.mapper.ChatMessageMapper;
import com.gok.pboot.portal.dto.ChatMessageDto;
import com.gok.pboot.portal.dto.ListConversationsDto;
import com.gok.pboot.portal.dto.ListMessagesDto;
import com.gok.pboot.portal.exception.ChatException;
import com.gok.pboot.portal.service.IChatMessageService;
import com.gok.pboot.portal.util.ChatMessagesUtils;
import com.gok.pboot.portal.vo.CompletionsVo;
import com.gok.pboot.portal.vo.HistoryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 国科小助手
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageMapper, DifyChatMessages> implements IChatMessageService {

    private final ChatMessageMapper chatMessageMapper;

    private final ChatMessagesUtils chatMessagesUtils;

    private final RedisTemplate redisTemplate;

    @Value("${spring.profiles.active}")
    private String profile;

    private static final String portal = "portal";

    /**
     * 新客服助手首次提示语
     *
     * @return 首次提示语
     */
    @Override
    public Map<String, Object> fastFirstTip() {
        return chatMessagesUtils.fastFirstTip();
    }

    /**
     * 新客服助手聊天
     *
     * @param content 聊天内容
     * @return {@link R}<{@link CompletionsVo}>
     */
    @Override
    public R<CompletionsVo> talk(String content) {
        // 校验登录状态
        PigxUser user = UserUtils.getUser();
        if (!Optional.ofNullable(user).isPresent()) {
            return R.failed();
        }

        return R.ok(chatMessagesUtils.completions(profile + "-" + user.getId(), content));
    }

    /**
     * 获取聊天列表
     *
     * @param page 当前页
     * @param limit 每页条数
     * @param currentSize 当前页条数
     * @return {@link List}<{@link HistoryVo}>
     */
    @Override
    public List<HistoryVo> getHistoryList(Long page, Long limit, Integer currentSize) {
        // 校验登录状态
        PigxUser user = UserUtils.getUser();
        if (!Optional.ofNullable(user).isPresent()) {
            return new ArrayList<>();
        }
        List<HistoryVo> historyList = chatMessagesUtils.getHistoryList(profile + "-" + user.getId(), page * limit + currentSize);
        int size = historyList.size() - currentSize;
        historyList = historyList.subList(0 , size);
        size = size < limit ? NumberUtils.INTEGER_ZERO : (int) (size / limit - page);
        return historyList.stream()
                .skip(size * limit)
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * dify 首次提示语
     *
     * @return Map
     */
    @Override
    public Map<String, Object> firstTip() {
        Map<String, Object> res = new HashMap<>();
        // 校验登录信息
        if (!Optional.ofNullable(UserUtils.getUser().getId()).isPresent()) {
            return new HashMap<>();
        }
        Map<String, Object> difyRes = chatMessagesUtils.firstTip();
        res.put("welcomeText",difyRes.get("opening_statement"));
        return res;
    }

    /**
     * dify 创建会话信息或基于此前的对话继续发送消息
     *
     * @param query 输入聊天内容
     * @return {@link R}<{@link ChatMessageDto}>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Object> chatMessages(String query) {
        // 校验登录信息
        Long userId = UserUtils.getUser().getId();
        String username = UserUtils.getUser().getName();
        if (!Optional.ofNullable(userId).isPresent() || !Optional.ofNullable(username).isPresent()) {
            return R.ok(new ChatMessageDto());
        }
        String chatUserId = profile + "-" + userId;
        Object conversationId = redisTemplate.opsForHash().get(String.format(RedisConstant.DIFY, portal), chatUserId);
        // 用户首次调用数据库中没有信息
        if (!Optional.ofNullable(conversationId).isPresent()) {
            //根据userId查询是否有数据
            DifyChatMessages chatMessages = chatMessageMapper.selectByUserId(userId);
            if(!Optional.ofNullable(chatMessages).isPresent()){
                ChatMessageDto chatMessageDto = (ChatMessageDto) chatMessagesUtils.chatMessage(query, "", chatUserId).getData();
                log.info("首次聊天:{}", chatMessageDto);
                if (!Optional.ofNullable(chatMessageDto).isPresent()) {
                    throw new ChatException("当前使用人数较多,请稍后再试");
                }

                DifyChatMessages difyChatMessages = new DifyChatMessages();
                difyChatMessages.setUserId(userId);
                difyChatMessages.setUser(username);
                difyChatMessages.setConversationId(chatMessageDto.getConversationId());
                chatMessageMapper.insert(difyChatMessages);

                redisTemplate.opsForHash().put(String.format(RedisConstant.DIFY, portal), String.valueOf(userId), chatMessageDto.getConversationId());
                return R.ok(chatMessageDto);
            }else{
                conversationId = chatMessages.getConversationId();
            }
        }

        return chatMessagesUtils.chatMessage(query, (String) conversationId, chatUserId);
    }

    /**
     * dify 小助手聊天记录
     *
     * @param firstId 第一条聊天记录id
     * @param limit 条数
     * @return {@link R}<{@link ListMessagesDto}>
     */
    @Override
    public R<Object> listMessages(String firstId, Integer limit) {
        // 校验登录信息
        Long userId = UserUtils.getUser().getId();
        String chatUserId = profile + "-" + userId;
        String username = UserUtils.getUser().getName();
        if (!Optional.ofNullable(userId).isPresent() || !Optional.ofNullable(username).isPresent()) {
            return R.ok(new ListMessagesDto());
        }

        Object conversationId = redisTemplate.opsForHash().get(String.format(RedisConstant.DIFY, portal), chatUserId);
        if (!Optional.ofNullable(conversationId).isPresent() && !Optional.ofNullable(conversationId).isPresent()) {
            DifyChatMessages chatMessages = chatMessageMapper.selectByUserId(userId);
            if(Optional.ofNullable(chatMessages).isPresent()){
                conversationId = chatMessages.getConversationId();
            }else{
                return R.ok(new ListMessagesDto());
            }
        }

        firstId = !Optional.ofNullable(firstId).isPresent() ? "" : firstId;
        limit = !Optional.ofNullable(limit).isPresent() ? 20 : limit;
        return chatMessagesUtils.listMessage((String) conversationId, chatUserId, firstId, limit);
    }


    /**
     * dify 删除掉rds的缓存key
     *
     * @param userId
     * @return
     */
    @Override
    public R delRdsKey(String userId) {
        String chatUserId = profile + "-" + userId;
        redisTemplate.opsForHash().delete(String.format(RedisConstant.DIFY, portal), chatUserId);
        return R.ok("删除成功！");
    }

    /**
     * 聊天会话列表（已弃用）
     *
     * @param lastId 最后一次聊天id
     * @param limit 条数
     * @return {@link R}<{@link ListConversationsDto}>
     */
    @Deprecated
    @Override
    public R<Object> listConversations(String lastId, Integer limit) {
        // 校验登录信息
        Long userId = UserUtils.getUser().getId();
        String username = UserUtils.getUser().getName();
        if (!Optional.ofNullable(userId).isPresent() || !Optional.ofNullable(username).isPresent()) {
            return R.ok(new ArrayList<>());
        }

        Object conversationId = redisTemplate.opsForHash().get(String.format(RedisConstant.DIFY, portal), String.valueOf(userId));
        if (!Optional.ofNullable(conversationId).isPresent()) {
            return R.ok(new ArrayList<>());
        }

        lastId = !Optional.ofNullable(lastId).isPresent() ? "" : lastId;
        return R.ok(chatMessagesUtils.conversations(userId, lastId, limit).getData());
    }

}
