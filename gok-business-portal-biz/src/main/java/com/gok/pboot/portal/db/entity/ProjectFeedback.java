package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目反馈表
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_feedback")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目反馈表")
public class ProjectFeedback extends Model<ProjectFeedback> {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 反馈类型（0:优化建议、1：功能异常、2:其他）
     */
    @ApiModelProperty(value = "反馈类型")
    private Integer type;

    /**
     * 应用Id
     */
    @ApiModelProperty(value = "应用Id")
    private Long applicationId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String applicationName;

    /**
     * 反馈内容
     */
    @ApiModelProperty(value = "反馈内容")
    private String content;

    /**
     * 处理情况（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    @ApiModelProperty(value = "处理情况")
    private Integer handlingSituation;

    /**
     * 文件ids，多个文件用,隔开
     */
    @ApiModelProperty(value = "文件ids")
    private String fileIds;

    /**
     * 服务态度
     */
    @ApiModelProperty(value = "服务态度")
    private Integer serviceAttitude;

    /**
     * 处理效果
     */
    @ApiModelProperty(value = "处理效果")
    private Integer serviceEffectiveness;

    /**
     * 处理效率
     */
    @ApiModelProperty(value = "处理效率")
    private Integer serviceEfficiency;

    /**
     * 服务感受
     */
    @ApiModelProperty(value = "服务感受")
    private String serviceFeel;


    /**
     * 反馈人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long feedbackUserId;

    /**
     * 反馈人name
     */
    @ApiModelProperty(value = "创建人Name")
    private String feedbackUserName;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    @ApiModelProperty(value = "逻辑删除")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

    /**
     * 排序顺序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
}