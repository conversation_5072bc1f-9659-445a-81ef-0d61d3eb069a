package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 禅道项目枚举
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
public enum ZenTaoProjectEnum implements ValueEnum<Integer> {

    /**
     * 数字化门户
     */
    PORTAL(44, "数字化门户"),

//    /**
//     * 产融平台
//     */
//    IOIE(46, "产融平台",""),

    /**
     * 河狸学途
     */
    HL(46, "河狸学途"),

//    /**
//     * 河狸学途-运管端
//     */
//    HL_MANAGE(46, "河狸学途-运管端"),

    /**
     * 云展台
     */
    DEMONSTRATION(36, "解决方案云展台"),

//    /**
//     * 云展台-管理后台
//     */
//    DEMONSTRATION_MANAGE(36, "云展台-管理后台"),

//    /**
//     * 项目管理系统
//     */
//    PMS(22, "项目管理系统"),

    /**
     * 业务一体化
     */
    YWYTH(22, "业务一体化"),

    /**
     * 人才供应链
     */
    TALENT(40, "人才供应链"),

    /**
     * EHR
     */
    EHR(19, "EHR管理系统"),

    /**
     * 数字财务
     */
    FINANCIAL(50, "数字财务"),

    /**
     * 经营分析
     */
    ANALYSIS(13, "经营分析"),

//    /**
//     * 业务中台
//     */
//    CENTRAL_PLATFORM(45, "业务中台"),

    /**
     * wiki--其他（暂无，归类于门户中）
     */
    WIKI(44, "wiki"),

    /**
     * 国科网盘（内部）--其他（暂无，归类于门户中）
     */
    NETWORK_DISK_IN(44, "国科网盘（内部）"),

    /**
     * 国科网盘（外部）--其他（暂无，归类于门户中）
     */
    NETWORK_DISK_OUT(44, "国科网盘（外部）"),

    /**
     * 国科校友小程序
     */
    XYXCX(57, "校友小程序"),
    ;


    private Integer value;

    private String name;


    ZenTaoProjectEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按应用程序名称获取值
     *
     * @param applicationName 应用程序名称
     * @return {@link Integer}
     */
    public static Integer getValueByApplicationName(String applicationName) {
        for (ZenTaoProjectEnum projectEnum : ZenTaoProjectEnum.values()) {
            if (applicationName.contains(projectEnum.name)) {
                return projectEnum.value;
            }
        }
        return PORTAL.value;
    }

}
