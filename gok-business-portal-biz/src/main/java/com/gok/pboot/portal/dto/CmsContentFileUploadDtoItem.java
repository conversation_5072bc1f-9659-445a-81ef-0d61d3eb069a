package com.gok.pboot.portal.dto;

import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 附件上传Dto类
 *
 * <AUTHOR>
 * @since 2023-08-03
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsContentFileUploadDtoItem {

    /**
     * id
     */
    private Long id;

    /**
     * 文件类型（0图片、1文件、2视频）
     * {@link com.gok.pboot.portal.enums.FileTypeEnum}
     */
    @IntegerVerify(name = "文件类型", min = 0, max = 2, required = true)
    private Integer fileType;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 视频key
     */
    @StringVerify(name = "视频key", maxLen = 255)
    private String videoKey;

    /**
     * 视频转码进度0%-100%
     */
    @IntegerVerify(name = "视频转码进度", min = 0, max = 100)
    private Integer videoProgress;

}
