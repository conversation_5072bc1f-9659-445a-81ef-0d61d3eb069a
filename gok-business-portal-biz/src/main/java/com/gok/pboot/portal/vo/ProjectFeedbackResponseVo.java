package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目反馈回复vo
 *
 * <AUTHOR>
 * @date 2023/08/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeedbackResponseVo {

    /**
     * 内容
     */
    private String responseContent;

    /**
     * 图片
     */
    private List<String> picture;

    /**
     * 开始时间
     */
    private LocalDateTime updateTime;
}
