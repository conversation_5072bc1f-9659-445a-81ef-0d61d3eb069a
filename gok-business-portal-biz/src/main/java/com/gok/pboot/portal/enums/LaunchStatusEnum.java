package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 上架状态枚举类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Getter
public enum LaunchStatusEnum implements ValueEnum<Integer> {

    /**
     * 上架
     */
    LAUNCH(0, "上架"),

    /**
     * 下架
     */
    REMOVAL(1, "下架");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    LaunchStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 判断字典id是否合法
     *
     * @param value 字典id
     * @return 合法-true 非法-false 空-false
     */
    public static boolean isValueLegal(Integer value) {
        if (!Optional.ofNullable(value).isPresent()) {
            return false;
        }
        Optional<LaunchStatusEnum> optional = Arrays.asList(LaunchStatusEnum.values()).stream()
                .filter(e -> value.equals(e.getValue()))
                .findAny();
        return optional.isPresent();
    }

}
