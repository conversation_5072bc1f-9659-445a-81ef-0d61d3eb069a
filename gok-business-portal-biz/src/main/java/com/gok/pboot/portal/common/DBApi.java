package com.gok.pboot.portal.common;

import com.alibaba.fastjson.JSONObject;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.portal.common.client.ForestClient;
import com.gok.pboot.portal.exception.ServiceException;
import com.gok.pboot.portal.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * DBApi接入
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Slf4j
@Component
public class DBApi {

    private final String DBAPI_TOKEN="DBAPI:PORTAL:TOKEN";

    /**
     * 是否获取成功的字段
     */
    public static final String SUCCESS = "success";

    /**
     * 获取到的字段
     */
    public static final String DATA = "data";

    /**
     * 获取到的字段
     */
    public static final String TOKEN = "token";

    /**
     * DbApi获取token的url地址
     */
    @Value("${dbapi.url}")
    private String url;

    /**
     * DbApi应用的appId
     */
    @Value("${dbapi.appid}")
    private String appId;

    /**
     * DbAp应用的secret
     */
    @Value("${dbapi.secret}")
    private String secret;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ForestClient forestClient;

    /**
     * 读取令牌
     * token获取缓存
     *
     * @return {@link String}
     */
    public String readToken() {
        Object token = redisTemplate.opsForValue().get(DBAPI_TOKEN);
        if (!Optional.ofNullable(token).isPresent()) {
            JSONObject jsonObject = forestClient.getDbApiToken(url, appId, secret);
            token = jsonObject.get(TOKEN);
            if (!Optional.ofNullable(token).isPresent()) {
                throw new BusinessException("DBAPI异常,获取不到token信息");
            }
            redisTemplate.opsForValue().set(DBAPI_TOKEN, token, NumberUtils.INTEGER_TWO, TimeUnit.HOURS);

            return (String) jsonObject.get(TOKEN);
        }
        try {
            forestClient.getOAUserId(url, (String) token, "", new ArrayList<>());
        } catch (Exception e) {
            log.info("token失效");
            JSONObject jsonObject = forestClient.getDbApiToken(url, appId, secret);
            token = jsonObject.get(TOKEN);
            redisTemplate.opsForValue().set(DBAPI_TOKEN, token, NumberUtils.INTEGER_TWO, TimeUnit.HOURS);
        }

        return (String) token;
    }

    /**
     * 获取项目字典
     *
     * @param mobile     字典值
     * @param statusList 状态列表
     * @return loginId
     */
    public List<UserVo> getOAUserId(String mobile, List<Integer> statusList) {
        JSONObject jsonObject = forestClient.getOAUserId(url, readToken(), mobile, statusList);
        if ((boolean) jsonObject.get(SUCCESS)){
            return (List<UserVo>) jsonObject.getJSONArray(DATA).toJavaList(UserVo.class);
        }

        throw new ServiceException("DBAPI异常,获取不到明细内容");
    }

    /**
     * 根据中台id获取人员id
     *
     * @param userId 字典值
     * @return loginId
     */
    public List<UserVo> getUserIdByZenTao(Long userId) {
        JSONObject jsonObject = forestClient.getUserIdByZenTao(url, readToken(), userId);
        if ((boolean) jsonObject.get(SUCCESS)){
            return jsonObject.getJSONArray(DATA).toJavaList(UserVo.class);
        }

        throw new ServiceException("DBAPI异常,获取不到明细内容");
    }

}
