package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 应用中心-最近应用
 *
 * <AUTHOR>
 * @TableName portal_application_recent
 * @date 2023/10/25
 */
@TableName(value ="portal_application_recent")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "应用中心-最近应用")
public class PortalApplicationRecent implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 应用id
     */
    private Long applicationId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

}