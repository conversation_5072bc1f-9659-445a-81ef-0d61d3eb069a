package com.gok.pboot.portal.controller;

import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.portal.dto.VodConfirmDto;
import com.gok.pboot.portal.service.VodService;
import com.gok.pboot.portal.vo.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 视频文件服务
 * <AUTHOR>  luoyq
 * @Description :  视频文件服务
 * @date :  2023/8/7 16:29
 * @menu HuaWeiYun视频服务
 */
@RestController
@RequestMapping("/vod")
@Slf4j
public class VodController {

    @Resource
    private VodService vodService;

    /**
     * 获取配置信息
     *
     * @return {@link HwYunVodConfigVo}
     */
    @GetMapping("getConfigInfo")
    @ApiOperation(value = "获取配置信息", notes = "获取配置信息")
    public HwYunVodConfigVo getConfigInfo() {
        return vodService.getConfigInfo();
    }

    /**
     * 获取身份验证
     *
     * @param fileName 文件名
     * @return {@link VodVo}
     */
    @GetMapping("/getAuth/{fileName}")
    @ApiOperation(value = "获取上传地址和凭证", notes = "获取上传地址和凭证")
    public VodVo getAuth(@PathVariable("fileName") String fileName) {
        return vodService.getAuth(fileName);
    }

    /**
     * 视频地址
     *
     * @param videoKey 视频键
     * @return {@link VodAddressVo}
     */
    @GetMapping("videoAddress/{videoKey}")
    @ApiOperation(value = "获取播放地址", notes = "获取播放地址")
    public VodAddressVo videoAddress(@PathVariable("videoKey") String videoKey) {
        return vodService.getAddress(videoKey);
    }


    /**
     * 反式代码
     *
     * @param keys 钥匙
     * @return {@link List}<{@link VodTransProgressVo}>
     */
    @GetMapping("transCodes")
    @ApiOperation(value = "获取转码进度", notes = "获取转码进度")
    public List<VodTransProgressVo> transCodes(@RequestParam List<String> keys) {
        return vodService.getTransProgress(keys);
    }

    /**
     * 获取 Trans 未完成
     *
     * @return {@link List}<{@link VodTransProgressVo}>
     */
    @Inner
    @PostMapping("trans/notCompleted")
    @ApiOperation(value = "获取转码未完成的视频", notes = "获取转码未完成的视频")
    public List<VodTransProgressVo> getTransNotCompleted() {
        return vodService.getTransNotCompleted();
    }

    /**
     * 获取 Trans 未完成 v2
     *
     * @param map 地图
     * @return {@link List}<{@link VodTransProgressVo}>
     */
    @Inner(value = false)
    @PostMapping("trans/notCompleted-V2")
    @ApiOperation(value = "阿里云获取转码未完成的视频", notes = "阿里云获取转码未完成的视频")
    public List<VodTransProgressVo> getTransNotCompletedV2(@RequestBody Map<String, Object> map) {
        return vodService.getTransNotCompletedV2(map);
    }

    /**
     * 确认视频
     *
     * @param vodConfirmDto VOD 确认 DTO
     * @return {@link VodConfirmVo}
     */
    @PostMapping("/confirmVideo")
    @ApiOperation(value = "确认媒资", notes = "确认媒资")
    public VodConfirmVo confirmVideo(@RequestBody VodConfirmDto vodConfirmDto) {
        return vodService.confirmVideo(vodConfirmDto);
    }
}
