package com.gok.pboot.portal.controller;


import com.gok.bcp.message.res.BasePageRes;
import com.gok.bcp.message.vo.BcpMessageVO;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.MessagePageDto;
import com.gok.pboot.portal.service.IMessageService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 消息控制器
 *
 * <AUTHOR>
 * @date 26/10/2023
 * @menu 消息控制器
 */
@RestController
@RequestMapping("/message")
public class MessageController {

    @Resource
    private IMessageService service;

    /**
     * 分页获取
     *
     * @param req 要求
     * @return {@link R}<{@link BasePageRes}<{@link BcpMessageVO}>>
     */
    @GetMapping("/page")
    public R<BasePageRes<BcpMessageVO>> page(MessagePageDto req){
        return service.page(req);
    }

    /**
     *
     * 单个已读
     *
     * @param id     编号
     * @return {@link R}
     */
    @PutMapping("/read/{id}")
    public R read(@PathVariable Long id){
        return service.updateMailById(id);
    }

    /**
     * 全部已读
     *
     * @return {@link R}
     */
    @PutMapping("/allRead")
    public R allRead(){
        return service.mailReadByUserId();
    }

    /**
     *
     * 通过用户id查询站内信未拉取的消息
     *
     * @return {@link R}<{@link List}<{@link BcpMessageVO}>>
     */
    @GetMapping("/getMailUnPullListByUserId")
    R<List<BcpMessageVO>> getMailUnPullListByUserId(){
        return new R<List<BcpMessageVO>>();
        //return service.getMailUnPullListByUserId();
    }
}
