package com.gok.pboot.portal.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * CDN img 实用程序
 *
 * <AUTHOR>
 * @date 2024/01/22
 */
@Slf4j
@Component
public class CdnImgUtils {

    @Value("${zen-tao.fileurl}")
    private String fileUrl;

    @Value("${file.bucketName}")
    private String bucketName;
    

    /**
     * URL拼接图片名
     *
     * @param file 文件名
     * @return 路径
     */
    public String createHttpUrl(String file){
        if (StrUtil.isEmpty(file)) {
            return "";
        }
        if ("/".equals(file.substring(0, 1))) {
            //return fileUrl + file.substring(file.lastIndexOf("/") + 1);
            return fileUrl + file.substring(file.lastIndexOf(bucketName+"/") + bucketName.length()+1);
        }
        return fileUrl + file;
    }

    /**
     * 图片拼接处理
     *
     * @param imagePath 图片原路径
     * @param thumbnailValue 需拼接的路径
     * @return 图片新路径
     */
    public String getThumbnail(String imagePath, String thumbnailValue) {
        if (StrUtil.isEmpty(imagePath)) {
            return "";
        }
        if (imagePath.endsWith("/")) {
            return String.format("%s?x-image-process=style/%s", imagePath.substring(0, imagePath.length() - 1), thumbnailValue);
        }
        return String.format("%s?x-image-process=style/%s", imagePath, thumbnailValue);
    }
}
