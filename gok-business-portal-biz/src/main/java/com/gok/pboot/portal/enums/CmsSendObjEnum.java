package com.gok.pboot.portal.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *内容发送对象枚举
 *
 * <AUTHOR>
 * @date 12/1/2024
 */
@Getter
@AllArgsConstructor
public enum CmsSendObjEnum {

    /**
     * 全员
     */
    ALL(0,"全员"),
    /**
     * 部门
     */
    DEPT(1,"部门"),
    /**
     * 用户
     */
    USER(2,"用户");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;
}
