package com.gok.pboot.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.portal.db.entity.ProjectFeedbackResponse;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackResponseMapper;
import com.gok.pboot.portal.service.IProjectFeedbackResponseService;

import org.springframework.stereotype.Service;


/**
 * <p>
 * 反馈回复表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Service
public class ProjectFeedbackResponseServiceImpl extends ServiceImpl<ProjectFeedbackResponseMapper, ProjectFeedbackResponse>
    implements IProjectFeedbackResponseService {

}




