package com.gok.pboot.portal.dto;

import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import com.gok.pboot.common.core.validation.annotation.LongVerify;
import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容管理新增/编辑 Dto类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Data
public class CmsContentSaveOrUpdateDto {

    /**
     * id
     */
    private Long id;

    /**
     * 内容类别
     * {@link com.gok.pboot.portal.enums.ContentCategoryEnum}
     */
    @IntegerVerify(name = "内容类别", min = 0, max = 10, required = true)
    private Integer contentCategory;

    /**
     * 标题
     */
    @StringVerify(name = "标题", maxLen = 50, required = true)
    private String title;

    /**
     * 发布者id
     */
    @LongVerify(name = "发布者id")
    private Long publisherId;

    /**
     * 发布者名称
     */
    @StringVerify(name = "发布者名称", maxLen = 32)
    private String publisherName;

    /**
     * 定时发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 上架状态（0已上架、1未上架）
     * {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     */
    @IntegerVerify(name = "上架状态", min = 0, max = 1, required = true)
    private Integer status;

    /**
     * 正文
     */
    private String contentText;

    /**
     * 业务分类
     * {@link com.gok.pboot.portal.enums.BusinessTypeEnum}
     */
    @IntegerVerify(name = "业务分类", min = 0)
    private Integer businessType;

    /**
     * 内容类型（0图文、1H5链接、2视频）
     * {@link com.gok.pboot.portal.enums.ContentTypeEnum}
     */
    @IntegerVerify(name = "内容类型", min = 0, max = 2)
    private Integer contentType;

    /**
     * 权重
     */
    @IntegerVerify(name = "权重", min = 0, max = 99)
    private Integer weight;

    /**
     * 关联内容id
     */
    @StringVerify(name = "关联内容id")
    private String relationContentId;

    /**
     * 链接地址
     */
    @StringVerify(name = "链接地址", maxLen = 500)
    private String linkAddress;

    /**
     * 封面图url
     */
    @StringVerify(name = "封面图url", maxLen = 100)
    private String coverImageUrl;

    /**
     * 视频key
     * 用于获取视频转码进度 当附件类型为视频时必传
     */
    private String videoKey;

    /**
     * 发送对象
     * {@link com.gok.pboot.portal.enums.CmsSendObjEnum}
     */
    @IntegerVerify(name = "发送对象", min = 0, max = 100, required = true)
    private Integer sendObj;

    /**
     * 内容权限列表
     */
    private List<Long> idList;

    /**
     * 发布类型
     */
    private Integer publishType;

}
