package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 反馈批转信息的表
 *
 * <AUTHOR>
 * @TableName project_feedback_log
 * @since 2023-08-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_feedback_log")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "反馈批转信息表")
public class ProjectFeedbackLog extends Model<ProjectFeedbackLog> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 反馈id
     */
    @ApiModelProperty(value = "反馈id")
    private Long feedbackId;

    /**
     * 操作类型（0确认、1处理中、2搁置、3已解决、4已关闭、5重新激活、6取消）
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operationType;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String operationContent;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operationUserName;


}