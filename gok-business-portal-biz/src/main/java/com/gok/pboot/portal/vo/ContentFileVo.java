package com.gok.pboot.portal.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 培训文件 Vo类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentFileVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 文件类型（0图片、1文件、2视频）
     */
    private Boolean fileType;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 视频key
     */
    private String videoKey;

    /**
     * 视频转码进度0%-100%
     */
    private Integer videoProgress;

    /**
     * 文件大小
     */
    private Long fileSize;
}
