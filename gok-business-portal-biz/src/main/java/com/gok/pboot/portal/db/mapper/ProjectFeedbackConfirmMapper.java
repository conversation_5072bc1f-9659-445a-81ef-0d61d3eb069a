package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.db.entity.ProjectFeedbackConfirm;
import com.gok.pboot.portal.db.entity.ProjectFeedbackResponse;
import com.gok.pboot.portal.dto.WorkOrderProcessedDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <P>
 * 针对表针对表【project_feedback_confirm(反馈确认表)】的数据库操作Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Mapper
public interface ProjectFeedbackConfirmMapper extends BaseMapper<ProjectFeedbackConfirm> {

    /**
     * 根据feedbackId查询反馈确认
     *
     * @param feedbackId 反馈id
     * @return
     */
    ProjectFeedbackConfirm getOneByFeedbackId(@Param("feedbackId") Long feedbackId);


    /**
     * 更新反馈确认表信息
     *
     * @param followedWorkerId 跟进人Id
     * @param followedWorker   跟进人Id
     * @param userName         更新人姓名
     * @param feedbackId       反馈Id
     */
    boolean updateByFeedBackId(@Param("followed_worker_id") Long followedWorkerId,
                               @Param("followed_worker") String followedWorker,
                               @Param("userName") String userName,
                               @Param("feedbackId") Long feedbackId);

    /**
     * @param followedWorkerId 跟进人Id
     * @param followedWorker   跟进人Id
     * @param userName         更新人姓名
     * @param feedbackId       反馈Id
     * @param status           更新状态
     */
    boolean generalUpdate(@Param("followed_worker_id") Long followedWorkerId,
                          @Param("followed_worker") String followedWorker,
                          @Param("userName") String userName,
                          @Param("feedbackId") Long feedbackId,
                          @Param("status") Integer status);

}




