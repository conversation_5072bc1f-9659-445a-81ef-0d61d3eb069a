package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 业务分类枚举类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Getter
public enum BusinessTypeEnum implements ValueEnum<Integer> {

    /**
     * 规章制度
     */
    REGULATIONS(0, "规章制度"),

    /**
     * 办事指南和表格
     */
    GUIDANCE(1, "办事指南和表格"),

    /**
     * 公司新闻
     */
    NEWS(2, "公司新闻"),

    /**
     * 行业资讯
     */
    INFORMATION(3, "行业资讯"),
    /**
     * 制度
     */
    SYSTEM(4, "制度"),
    /**
     * 标准
     */
    STANDARD(5, "标准"),
    /**
     * 流程
     */
    PROCESS(6, "流程"),
    /**
     * 机制
     */
    MECHANISM(7, "机制"),
    /**
     * 计划
     */
    PLAN(8, "计划"),
    /**
     * 通报
     */
    NOTIFY(9, "通报"),
    /**
     * 决定
     */
    DECISION(10,"决定"),
    /**
     * 通用方案
     */
    GENERAL_SOLUTION(11, "通用方案"),
    /**
     * 行业方案
     */
    INDUSTRY_SOLUTION(12, "行业方案"),

    /**
     * 项目案例
     */
    PROJECT_CASE(13,"项目案例");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    BusinessTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
