package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容附件 Vo类
 *
 * <AUTHOR>
 * @since 2023-08-04
 **/
@Data
public class CmsContentFileVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 文件类型字典id
     */
    private Integer fileType;

    /**
     * 文件类型字典值
     */
    private String fileTypeTxt;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 视频key
     */
    private String videoKey;

    /**
     * 视频转码进度0%-100%
     */
    private Integer videoProgress;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 文件原始名称
     */
    private String original;

    /**
     * 访问路径
     */
    private String fileUrl;

    /**
     * 文件名称
     */
    private String fileName;

}
