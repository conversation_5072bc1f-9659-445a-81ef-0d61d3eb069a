package com.gok.pboot.portal.db.mapper;

import com.gok.pboot.portal.db.entity.CmsContentAuth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CMS 内容身份验证映射器
 *
 * <AUTHOR>
 * @description 针对表【cms_content_auth(内容权限表)】的数据库操作Mapper
 * @createDate 2024-01-12 09:20:36
 * @Entity com.gok.pboot.portal.db.entity.CmsContentAuth
 * @date 2024/01/12
 */
public interface CmsContentAuthMapper extends BaseMapper<CmsContentAuth> {

    /**
     * 按内容、用户 ID 批量保存
     *
     * @param contentId  内容 ID
     * @param userIdList 用户 ID 列表
     * @param createBy   创建者
     */
    void batchSaveByUserId(@Param("contentId") Long contentId,@Param("userIdList") List<Long> userIdList,@Param("createBy") String createBy);


    /**
     * 按内容、部门 ID 批量保存
     *
     * @param contentId  内容 ID
     * @param userIdList 用户 ID 列表
     * @param createBy   创建者
     */
    void batchSaveByDeptId(@Param("contentId") Long contentId,@Param("deptIdList") List<Long> userIdList,@Param("createBy") String createBy);

    /**
     * 按 Content ID 批量物理删除
     *
     * @param contentId 内容 ID
     */
    void batchDeleteByContentId(@Param("contentId") Long contentId);

    /**
     * 按用户 ID 获取有权限的内容
     *
     * @param userId 用户 ID
     * @return {@link List}<{@link Long}>
     */
    List<Long> getAuthContentByUserId(@Param("userId") Long userId);

    /**
     * 按 Content ID 列表批量删除
     *
     * @param contentIdList Content ID 列表
     */
    void batchDeleteByContentIdList(@Param("contentIdList") List<Long> contentIdList);

    /**
     * 按内容 ID 获取部门 ID 列表
     *
     * @param contentId 内容 ID
     * @return {@link List}<{@link Long}>
     */
    List<Long> getDeptIdListByContentId(@Param("contentId") Long contentId);

    /**
     * 按内容 ID 获取用户 ID 列表
     *
     * @param contentId 内容 ID
     * @return {@link List}<{@link Long}>
     */
    List<Long> getUserIdListByContentId(@Param("contentId") Long contentId);

    /**
     * 按部门 ID 进行身份验证
     *
     * @param contentId 内容 ID
     * @param deptIds        部门编号
     * @return {@link Integer}
     */
    Integer isAuthByDeptId(@Param("contentId")Long contentId,@Param("deptIds") List<Long> deptIds);

    /**
     * 按用户 ID 进行身份验证
     *
     * @param contentId 内容 ID
     * @param userId        编号
     * @return {@link Integer}
     */
    Integer isAuthByUserId(@Param("contentId")Long contentId,@Param("userId") Long userId);
}




