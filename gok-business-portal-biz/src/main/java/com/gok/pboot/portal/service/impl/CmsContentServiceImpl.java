package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.SysDept;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.CmsContent;
import com.gok.pboot.portal.db.mapper.CmsContentMapper;
import com.gok.pboot.portal.dto.CmsContentDto;
import com.gok.pboot.portal.dto.CmsContentSaveOrUpdateDto;
import com.gok.pboot.portal.enums.*;
import com.gok.pboot.portal.service.*;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.util.EnumUtils;
import com.gok.pboot.portal.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CMS内容服务实现
 *
 * <AUTHOR>
 * @date 2024/01/12
 * @since 2023-07-31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CmsContentServiceImpl extends ServiceImpl<CmsContentMapper, CmsContent> implements ICmsContentService {

    private final CmsContentMapper cmsContentMapper;

    private final ICmsOperationLogService operationLogService;

    private final ICmsContentFileService cmsContentFileService;

    private final VodService vodService;

    private final CdnImgUtils cdnImgUtils;

    private final RemoteMailService remoteMailService;

    private final CmsContentAuthService cmsContentAuthService;

    // 默认关联id
    private static final Long DEFAULT_RELATION_ID = 0L;
    // 视频转码完成进度
    private static final Long TRANSCODE_SUCCEED_PROGRESS = 100L;

    @Value("${recent-router.content}")
    private String recentRouter;

    @Override
    public Page<CmsContentVo> findPage(Page page, CmsContentDto request) {
        Page<CmsContentVo> voPage = new Page<>();

        Page<CmsContent> pageRes;
        // 是否根据权限分页查询
        if (request.isSelAll()) {
            checkRole();
            pageRes = baseMapper.findPage(page, request);
        }else {
            PigxUser pigxUser = SecurityUtils.getUser();
            pageRes = baseMapper.findPageByAuth(page,request, pigxUser.getId(),pigxUser.getDeptList().stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        }

        BeanUtil.copyProperties(pageRes, voPage, "records");

        List<CmsContent> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return voPage;
        }

        // 附件相关信息获取
        List<Long> contentIds = records.stream().map(CmsContent::getId).collect(Collectors.toList());
        List<CmsContentFileVo> contentFileVoList = cmsContentFileService.findByContentIdList(contentIds);
        Map<Long, List<CmsContentFileVo>> contentFileMap = CollUtil.isEmpty(contentFileVoList)
                ? new HashMap<>()
                : contentFileVoList.stream().collect(Collectors.groupingBy(CmsContentFileVo::getContentId));

        // 关联内容信息获取
        List<Long> relationIds = records.stream()
                .filter(e -> e.getRelationContentId() != null && !e.getRelationContentId().equals(DEFAULT_RELATION_ID))
                .map(CmsContent::getRelationContentId)
                .collect(Collectors.toList());
        Map<Long, CmsContent> relationMap = new HashMap<>();
        if (CollUtil.isNotEmpty(relationIds)) {
            CmsContentDto relationRequest = CmsContentDto.builder()
                    .status(ShowFlagEnum.YES.getValue())
                    .publishTime(DateUtil.now())
                    .build();
            List<CmsContent> relationContentList = baseMapper.findByIdList(relationIds, relationRequest);
            if (CollUtil.isNotEmpty(relationContentList)) {
                relationMap.putAll(relationContentList.stream()
                        .collect(Collectors.toMap(CmsContent::getId, Function.identity())));
            }
        }

        // 封装响应信息
        List<CmsContentVo> voList = new ArrayList<>();
        records.forEach(r -> {
            CmsContentVo voItem = BeanUtil.copyProperties(r, CmsContentVo.class);
            voItem.setBusinessTypeTxt(EnumUtils.getNameByValue(BusinessTypeEnum.class, r.getBusinessType()));
            voItem.setContentTypeTxt(EnumUtils.getNameByValue(ContentTypeEnum.class, r.getContentType()));
            voItem.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, r.getStatus()));
            voItem.setContentCategoryTxt(EnumUtils.getNameByValue(ContentCategoryEnum.class, r.getContentCategory()));
            voItem.setFileList(contentFileMap.getOrDefault(voItem.getId(), new ArrayList<>()));
            if (Optional.ofNullable(voItem.getRelationContentId()).isPresent()) {
                CmsContent relationContent = relationMap.getOrDefault(voItem.getRelationContentId(), null);
                Integer relationShowFlag = Optional.ofNullable(relationContent).isPresent()
                        ? ShowFlagEnum.YES.getValue()
                        : ShowFlagEnum.NO.getValue();
                voItem.setRelationShowFlag(relationShowFlag);
            }
            voList.add(voItem);
        });

        for (CmsContentVo cmsContentVo : voList) {
            if (!FileTypeEnum.VIDEO.getValue().equals(cmsContentVo.getContentType())) {
                if (ContentCategoryEnum.NEWS.getValue().equals(cmsContentVo.getContentCategory())) {
                    cmsContentVo.setCoverImageUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(cmsContentVo.getCoverImageUrl()), ThumbnailEnum.COVER_IMAGE_NEWS_INFO.getValue()));
                } else {
                    cmsContentVo.setCoverImageUrl(cdnImgUtils.createHttpUrl(cmsContentVo.getCoverImageUrl()));
                }
            }else {
                for (CmsContentFileVo cmsContentFileVo : cmsContentVo.getFileList()) {
                    if(StringUtils.isNotEmpty(cmsContentFileVo.getVideoKey())){
                        VodAddressVo vodAddressVo = vodService.getAddress(cmsContentFileVo.getVideoKey());
                        cmsContentVo.setCoverImageUrl(vodAddressVo == null ? null: vodAddressVo.getCoverAddress());
                    }
                }
            }
        }

        voPage.setTotal(pageRes.getTotal());
        voPage.setRecords(voList);
        return voPage;
    }


        @Override
        public CmsContentDetailVo findDetail(Long contentId,boolean isAdmin) {

        CmsContent cmsContent = cmsContentMapper.selectById(contentId);

        if (!Optional.ofNullable(cmsContent).isPresent() ) {
            //不存在或是下架
            return new CmsContentDetailVo();
        }
        //校验权限
        if (isAdmin) {
            //校验是否有后台权限
            checkRole();
        }else if (Boolean.FALSE.equals(cmsContentAuthService.isAuthByCmsId(contentId,cmsContent.getSendObj())) || LocalDateTime.now().isBefore(cmsContent.getPublishTime()) || LaunchStatusEnum.REMOVAL.getValue().equals(cmsContent.getStatus())){
            //没有权限或者是发布时间在现在之后或者下架
            return new CmsContentDetailVo();
        }

        String coverImageUrlSuffix = cmsContent.getCoverImageUrl();
        if (!FileTypeEnum.VIDEO.getValue().equals(cmsContent.getContentType())) {
            if (ContentCategoryEnum.NEWS.getValue().equals(cmsContent.getContentCategory())) {
                cmsContent.setCoverImageUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(cmsContent.getCoverImageUrl()), ThumbnailEnum.COVER_IMAGE_NEWS_INFO.getValue()));
            } else {
                cmsContent.setCoverImageUrl(cdnImgUtils.createHttpUrl(cmsContent.getCoverImageUrl()));
            }
        }
        CmsContentDetailVo cmsContentDetailVo = CmsContentDetailVo.build(cmsContent);
        cmsContentDetailVo.setCoverImageUrlSuffix(coverImageUrlSuffix);

        //赋值idList
        cmsContentDetailVo.setIdList(cmsContentAuthService.getIdListByContentId(cmsContentDetailVo.getSendObj(),contentId));

        return cmsContentDetailVo;
    }

    /**
     * 检查是否有权限
     *
     * @return boolean
     */
    @PreAuthorize("@pms.hasPermission('content_manage')")
    private void checkRole() {
        return;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(CmsContentSaveOrUpdateDto request) {

        // 赋值实体类
        CmsContent cmsContent = buildSave(request);

        //赋值id
        Long contentId = IdWorker.getId();
        cmsContent.setId(contentId);

        // 视频类型根据转码状态设置标记位
        Integer showFlag = getVideoShowFlag(request);
        cmsContent.setShowFlag(showFlag);

        // 根据发布时间设置上架时间
        if (Optional.ofNullable(cmsContent.getPublishTime()).isPresent()) {
            cmsContent.setLaunchTime(cmsContent.getPublishTime());
        } else {
            cmsContent.setLaunchTime(LocalDateTime.now());
            cmsContent.setPublishTime(LocalDateTime.now());
        }

        //根据发送对象修改内容权限表
        cmsContentAuthService.batchSaveByObj(request.getSendObj(),contentId,request.getIdList());

        //如果是上架且立即发布则同步消息到中台
        if (LaunchStatusEnum.LAUNCH.getValue().equals(cmsContent.getStatus()) && CmsPublishTypeEnum.NOW.getValue().equals(request.getPublishType())) {
            syncToPlatformByType(cmsContent);
            cmsContent.setIsSend(CmsSendEnum.SEND.getValue());
        }

        // 持久化数据库
        baseMapper.insert(cmsContent);

        // 同步操作日志 (新增)
        operationLogService.syncCmsOperationLog(contentId, cmsContent.getTitle(), OperationTypeEnum.CREATE);

        return contentId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(CmsContentSaveOrUpdateDto request) {
        Long contentId = request.getId();
        Assert.isTrue(Optional.ofNullable(contentId).isPresent(), "id不能为空");

        // 修改内容信息
        CmsContent cmsContent = buildUpdate(request);
        Integer showFlag = getVideoShowFlag(request);
        if (Optional.ofNullable(showFlag).isPresent()) {
            cmsContent.setShowFlag(showFlag);
        }

        //避免更新出现为null的情况
        cmsContent.setRelationContentId(cmsContent.getRelationContentId()==null?0:cmsContent.getRelationContentId());

        // 同步操作日志 (编辑)
        operationLogService.syncCmsOperationLog(contentId, cmsContent.getTitle(), OperationTypeEnum.EDIT);

        //如果是下架改为上架则且立即发布同步消息到中台
        CmsContent entity = this.getById(contentId);
        Assert.isTrue(ObjectUtil.isNotNull(entity),"内容不存在");
        if (LaunchStatusEnum.LAUNCH.getValue().equals(request.getStatus()) && LaunchStatusEnum.REMOVAL.getValue().equals(entity.getStatus()) &&  CmsPublishTypeEnum.NOW.getValue().equals(request.getPublishType())) {
            syncToPlatformByType(cmsContent);
            cmsContent.setIsSend(CmsSendEnum.SEND.getValue());
        }

        //批量删除权限表
        cmsContentAuthService.batchDeleteByContentId(contentId);
        //批量新增权限表
        cmsContentAuthService.batchSaveByObj(request.getSendObj(),contentId,request.getIdList());

        //由定时发布改为立即发布则发布时间为当前
        if (CmsPublishTypeEnum.REGULAR.getValue().equals(entity.getPublishType()) &&  CmsPublishTypeEnum.NOW.getValue().equals(cmsContent.getPublishType())) {
            cmsContent.setPublishTime(LocalDateTime.now());
        }

        // 根据发布时间修改上架时间
        if (Optional.ofNullable(cmsContent.getPublishTime()).isPresent()) {
            cmsContent.setLaunchTime(cmsContent.getPublishTime());
        }else {
            cmsContent.setPublishTime(LocalDateTime.now());
            cmsContent.setLaunchTime(LocalDateTime.now());
        }

        baseMapper.updateById(cmsContent);

        return contentId;
    }

    /**
     * 根据新增/编辑请求获取内容展示标记
     *
     * @param request 新增/编辑内容请求
     * @return 展示标记
     */
    private Integer getVideoShowFlag(CmsContentSaveOrUpdateDto request) {
        Integer showFlag = null;
        // 视频类型内容 根据视频key获取视频转码进度 转码完成才可展示
        if (ContentTypeEnum.VIDEO.getValue().equals(request.getContentType())) {
            String videoKey = request.getVideoKey();
            List<VodTransProgressVo> transProgressVos = vodService.getTransProgress(Arrays.asList(videoKey));
            VodTransProgressVo transProgressVo = CollUtil.isNotEmpty(transProgressVos)
                    ? transProgressVos.get(0)
                    : new VodTransProgressVo();
            if (TRANSCODE_SUCCEED_PROGRESS.equals(transProgressVo.getTransCode())) {
                showFlag = ShowFlagEnum.YES.getValue();
            } else {
                showFlag = ShowFlagEnum.NO.getValue();
            }
        }
        return showFlag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateStatus(Long id, Integer status) {
        Assert.isTrue(LaunchStatusEnum.isValueLegal(status), "状态字段非法");

        CmsContent cmsContent = baseMapper.selectById(id);
        if (!Optional.ofNullable(cmsContent).isPresent()) {
            return id;
        }

        // 修改上下架信息
        baseMapper.updateStatusById(id, status, SecurityUtils.getUser().getUsername(), DateUtil.now());

        // 同步操作日志
        OperationTypeEnum operationType = LaunchStatusEnum.LAUNCH.getValue().equals(status)
                ? OperationTypeEnum.UP
                : OperationTypeEnum.DOWN;
        operationLogService.syncCmsOperationLog(id, cmsContent.getTitle(), operationType);

        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchDelete(List<Long> ids) {
        // 根据id集合批量逻辑删除
        if (CollUtil.isEmpty(ids)) {
            log.warn("内容id集合为空,批量删除操作结束！");
            return ids;
        }
        baseMapper.logicDeleteByIdList(ids);

        // 删除轮播图关联关系
        baseMapper.updateRelationByIdList(ids);

        //批量删除权限
        cmsContentAuthService.batchDeleteByContentIdList(ids);

        return ids;
    }

    /**
     * 根据新增请求实体构建内容信息实体
     *
     * @param request 新增请求实体
     * @return 内容信息实体
     */
    private CmsContent buildSave(CmsContentSaveOrUpdateDto request) {
        CmsContent cmsContent = BeanUtil.copyProperties(request, CmsContent.class);
        cmsContent.setCreateUserName(SecurityUtils.getUser().getName());
        return cmsContent;
    }

    /**
     * 根据编辑请求实体构建内容信息实体
     *
     * @param request 新增请求实体
     * @return 内容信息实体
     */
    private CmsContent buildUpdate(CmsContentSaveOrUpdateDto request) {
        String[] ignoreProperties = {"contentCategory"};
        return BeanUtil.copyProperties(request, CmsContent.class, ignoreProperties);
    }

    /**
     * 同步到中台
     *
     * @param title      标题
     * @param targetType 目标类型
     * @param targetList 目标列表
     * @param redirectUrl 跳转网址
     * @param cmsContent
     */
    private void syncToPlatform(String title, CmsContent cmsContent, String targetType, ArrayList<BcpMessageTargetDTO> targetList, String redirectUrl){
        //封装对象
        MailModel mailModel = new MailModel();
        //固定字段
        mailModel.setSource(SourceEnum.PORTAL_FRONT.getValue());
        mailModel.setSenderId(cmsContent.getPublisherId());
        mailModel.setSender(cmsContent.getPublisherName());
        mailModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
        //可变字段
        mailModel.setTitle(title);
        mailModel.setContent(title);
        mailModel.setTargetType(targetType);
        mailModel.setTargetList(targetList);
        mailModel.setRedirectUrl(redirectUrl);
        log.info("消息推送类型：{}，消息推送集合：{}" , mailModel.getTargetType(),mailModel.getTargetList());
        //发送消息
        remoteMailService.sendMsg(mailModel);
    }

    /**
     * 按类型同步到中台
     *
     * @param request 请求
     */
    private void syncToPlatformByType(CmsContent request){

        String sendType;
        ArrayList<BcpMessageTargetDTO> targetHandleList = new ArrayList<>();

// 使用map存储发送对象类型和目标类型的映射关系
        Map<Integer, String> objTypeToTargetType = new HashMap<>();
        objTypeToTargetType.put(CmsSendObjEnum.DEPT.getValue(), TargetTypeEnum.DEPT.getValue());
        objTypeToTargetType.put(CmsSendObjEnum.USER.getValue(), TargetTypeEnum.USERS.getValue());

// 获取发送对象类型对应的目标类型，如果没有找到则默认为全员
        sendType = objTypeToTargetType.getOrDefault(request.getSendObj(), TargetTypeEnum.ALL.getValue());

// 获取发送对象类型对应的ID列表，并将其转换为目标DTO列表
        cmsContentAuthService.getIdListByContentId(request.getSendObj(), request.getId()).forEach(e -> {
            BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
                    .targetId(e.toString())
                    .targetName(request.getTitle()).build();
            targetHandleList.add(targetDTO);
        });


        //通知公告
        if(ContentCategoryEnum.NOTIFICATION.getValue().equals(request.getContentCategory())){
            syncToPlatform("您有新的待阅文档(通知公告)",request, sendType,targetHandleList,recentRouter + request.getId());
        }
        //规章制度
        else if(BusinessTypeEnum.REGULATIONS.getValue().equals(request.getBusinessType())){
            syncToPlatform("您有新的待阅文档(规章制度)",request, sendType,targetHandleList,recentRouter  + request.getId());
        }
        //办事指南与表格
        else if (BusinessTypeEnum.GUIDANCE.getValue().equals(request.getBusinessType())) {
            syncToPlatform("您有新的待阅文档(办事指南与表格)",request, sendType,targetHandleList,recentRouter + request.getId());
        }
        //公文
        else if (ContentCategoryEnum.DOCUMENT.getValue().equals(request.getContentCategory())) {
            syncToPlatform("您有新的待阅公文",request, sendType,targetHandleList,recentRouter + request.getId());
        }
        //周报
        else if (ContentCategoryEnum.WEEKLY.getValue().equals(request.getContentCategory())) {
            syncToPlatform("本周国科周报已更新",request, sendType,targetHandleList,recentRouter + request.getId());
        }
    }

    @Override
    public List<CmsContentPublishVo> sendMsgByPublishTime() {
        //获取发布时间一分钟的内容
        List<CmsContent> cmsContents = baseMapper.getByPublishTime();
        log.info("进行按发布时间推送消息的定时任务{}",cmsContents);
        //发送消息
        ArrayList<Long> idList = new ArrayList<>();
        List<CmsContentPublishVo> cmsContentPublishVos = cmsContents.stream().map(c -> {
            log.info("定时推送推送内容{}",c);
            syncToPlatformByType(c);
            idList.add(c.getId());
            return BeanUtil.copyProperties(c, CmsContentPublishVo.class);
        }).collect(Collectors.toList());

        //改变内容发送状态
        if (CollUtil.isNotEmpty(idList)) {
            baseMapper.changeSendStatus(idList);
        }

        return cmsContentPublishVos;
    }

    @Override
    public CmsDocCountVo findSortCount() {
        PigxUser user = SecurityUtils.getUser();

        List<CmsSortCountVo> list = baseMapper.findSortCount(user.getId(),user.getDeptList().stream().map(SysDept::getDeptId).collect(Collectors.toList()));

        //计算总数
        CmsSortCountVo all = new CmsSortCountVo();

        all.setCount(list.stream()
                .mapToInt(e -> e.getCount())
                .reduce(0, (acc, count) -> acc + count));

        all.setBusinessType("all");


        list.add(all);

        return new CmsDocCountVo(list);
    }
}