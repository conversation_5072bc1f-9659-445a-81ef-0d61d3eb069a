package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalApplicationCategory;
import com.gok.pboot.portal.db.mapper.PortalApplicationCategoryMapper;
import com.gok.pboot.portal.dto.PortalApplicationCategoryDto;
import com.gok.pboot.portal.dto.PortalApplicationCategoryDtoItem;
import com.gok.pboot.portal.service.IPortalApplicationCategoryService;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.PortalApplicationCategoryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-01
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class PortalApplicationCategoryServiceImpl extends ServiceImpl<PortalApplicationCategoryMapper, PortalApplicationCategory> implements IPortalApplicationCategoryService {

    @Autowired
    private PortalApplicationCategoryMapper portalApplicationCategoryMapper;

    @Override
    public List<PortalApplicationCategoryVo> getAllCategory() {
        return portalApplicationCategoryMapper.queryCategoryWithCount();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R editList(PortalApplicationCategoryDto dto) {
        //全部逻辑删除
        portalApplicationCategoryMapper.delFlagAll(UserUtils.getUser().getName());

        List<PortalApplicationCategory> portalApplicationCategoryList = new ArrayList<>();

        List<@Valid PortalApplicationCategoryDtoItem> dtoList = dto.getCategoryList();

        int sortOrder = 0;
        //复制到实体类
        for (PortalApplicationCategoryDtoItem item : dtoList) {
            PortalApplicationCategory portalApplicationCategory = new PortalApplicationCategory();
            BeanUtil.copyProperties(item, portalApplicationCategory);
            portalApplicationCategory.setUpdateBy(UserUtils.getUser().getName());
            portalApplicationCategory.setUpdateTime(LocalDateTime.now());
            //恢复逻辑删除
            portalApplicationCategory.setDelFlag("0");
            //添加排序
            portalApplicationCategory.setSortOrder(sortOrder++);

            portalApplicationCategoryList.add(portalApplicationCategory);
        }

        //批量修改
        this.saveOrUpdateBatch(portalApplicationCategoryList);
        return R.ok("保存成功");
    }


}
