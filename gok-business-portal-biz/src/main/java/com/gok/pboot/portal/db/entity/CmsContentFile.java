package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 内容附件
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cms_content_file")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "内容附件")
public class CmsContentFile extends Model<CmsContentFile> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 内容id
     */
    @ApiModelProperty(value = "内容id")
    private Long contentId;

    /**
     * 文件类型（0图片、1文件、2视频）
     */
    @ApiModelProperty(value = "文件类型")
    private Integer fileType;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Long fileId;

    /**
     * 视频key
     */
    @ApiModelProperty(value = "视频key")
    private String videoKey;

    /**
     * 视频转码进度0%-100%
     */
    @ApiModelProperty(value = "视频转码进度0%-100%")
    private Integer videoProgress;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * delFlag
     */
    @TableLogic
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

}