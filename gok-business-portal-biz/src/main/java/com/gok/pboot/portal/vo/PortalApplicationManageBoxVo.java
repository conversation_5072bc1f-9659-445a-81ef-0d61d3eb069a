package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 应用管理下拉框vo
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortalApplicationManageBoxVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 应用跳转链接
     */
    private String linkAddress;
}
