package com.gok.pboot.portal.db.mapper;

import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.PortalApplicationCenter;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.ApplicationViewVo;
import com.gok.pboot.portal.vo.PortalApplicationCategoryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 应用中心-常用应用 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Mapper
public interface PortalApplicationCenterMapper extends BaseMapper<PortalApplicationCenter> {

    /**
     * 通过多表查询，找到常用应用并按sort排序
     * @param userId 用户id
     * @return 常用应用列表
     */
    List<ApplicationIconVo> queryUsefulApplication(@Param("userId") Long userId);


    /**
     * 根据多表查询，按分类找到非常用应用的排序,applicationIds为空则查所有
     * @param categoryId 应用分类Id
     * @param applicationIds 应用权限的Ids
     * @return 列表
     */
    List<ApplicationIconVo> queryNoUsefulByCategoryId(@Param("categoryId") Long categoryId,@Param("applicationIds") List<Long> applicationIds);

    /**
     * 按类别 ID 查询所有应用
     *
     * @param categoryId     类别 ID
     * @param applicationIds 应用程序 ID
     * @return {@link List}<{@link ApplicationViewVo}>
     */
    List<ApplicationViewVo> queryAllByCategoryIds(@Param("categoryId") Long categoryId,@Param("applicationIds") List<Long> applicationIds);
}
