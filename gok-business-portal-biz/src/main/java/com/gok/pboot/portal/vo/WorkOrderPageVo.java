package com.gok.pboot.portal.vo;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单管理--列表Vo
 *
 * <AUTHOR>
 * @date 28/8/2023
 */
@Data
public class WorkOrderPageVo {
    /**
     * ID
     */
    private Long id;
    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型（0:优化建议、1：功能异常、2:其他）
     */
    private Integer type;

    /**
     * 反馈类型Ste（0:优化建议、1：功能异常、2:其他）
     */
    private String typeStr;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 处理情况（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    private Integer handlingSituation;

    /**
     * 处理情况（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    private String handlingSituationStr;


    /**
     * 跟进人
     */
    private String followedWorker;

    /**
     * 禅道bug地址
     */
    private String zentaoUrl;
}
