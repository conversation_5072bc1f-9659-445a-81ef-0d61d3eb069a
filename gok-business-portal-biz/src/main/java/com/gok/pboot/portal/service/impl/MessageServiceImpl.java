package com.gok.pboot.portal.service.impl;

import com.gok.bcp.message.entity.enums.MsgStatusEnum;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.message.feign.RemoteMessageService;
import com.gok.bcp.message.res.BasePageRes;
import com.gok.bcp.message.vo.BcpMessageVO;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.dto.MessagePageDto;
import com.gok.pboot.portal.service.IMessageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 消息服务 IMPL
 *
 * <AUTHOR>
 * @date 26/10/2023
 */
@Service
public class MessageServiceImpl implements IMessageService {

    @Resource
    private RemoteMessageService remoteMessageService;

    @Resource
    private RemoteMailService remoteMailService;


    @Override
    public R<BasePageRes<BcpMessageVO>> page(MessagePageDto req) {
        Boolean countable = req.getCountable();
        int pageNo = req.getPageNo();
        int pageSize = req.getPageSize();
        String status = req.getStatus();
        Long userId = SecurityUtils.getUser().getId();
        //status 4 已读，5未读 ；countable是否获得总数 默认false
        return remoteMessageService.page(countable, pageNo, pageSize, status, userId);
    }

    @Override
    public R updateMailById(Long id) {
        String status = MsgStatusEnum.READ.getValue();
        return  remoteMailService.updateMailById(id, status);
    }

    @Override
    public R mailReadByUserId() {
        Long userId = SecurityUtils.getUser().getId();
        return  remoteMailService.mailReadByUserId(userId);
    }

    @Override
    public R<List<BcpMessageVO>> getMailUnPullListByUserId() {

        return remoteMailService.getMailUnPullListByUserId(SecurityUtils.getUser().getId());
    }
}
