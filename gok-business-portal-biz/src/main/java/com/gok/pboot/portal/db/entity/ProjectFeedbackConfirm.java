package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 反馈确认表
 *
 * <AUTHOR>
 * @TableName project_feedback_confirm
 * @since 2023-08-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_feedback_confirm")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "反馈确认表")
public class ProjectFeedbackConfirm extends Model<ProjectFeedbackConfirm> {
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 反馈id
     */
    @ApiModelProperty(value = "反馈id")
    private Long feedbackId;

    /**
     * 跟进人
     */
    @ApiModelProperty(value = "跟进人")
    private String followedWorker;

    /**
     * 跟进人ID
     */
    @ApiModelProperty(value = "跟进人ID")
    private Long followedWorkerId;

    /**
     * 处理人员
     */
    @ApiModelProperty(value = "处理人员")
    private String handleWorker;

    /**
     * 处理人员ID
     */
    @ApiModelProperty(value = "处理人员ID")
    private Long handleWorkerId;

    /**
     * 紧急程度（0一般，1紧急，2紧急，3特别紧急）
     */
    @ApiModelProperty(value = "紧急程度")
    private Integer urgencyDegree;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 禅道bugId
     */
    @ApiModelProperty(value = "禅道bugId")
    private Long zentaoId;

    /**
     * 禅道bug地址
     */
    @ApiModelProperty(value = "禅道bug地址")
    private String zentaoUrl;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

}