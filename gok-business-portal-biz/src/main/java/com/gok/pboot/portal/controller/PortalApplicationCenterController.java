package com.gok.pboot.portal.controller;

import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.PortalApplicationCenterDto;
import com.gok.pboot.portal.service.IPortalApplicationCenterService;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.ApplicationViewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 应用中心表
 * <AUTHOR>
 * @description 应用中心表
 * @menu 应用中心
 * @since 2023-08-02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/portalApplicationCenter")
@Api(tags = "应用中心表")
public class PortalApplicationCenterController {

    @Autowired
    private IPortalApplicationCenterService portalApplicationCenterService;

    /**
     * 编辑后的保存，编辑常用的应用及其顺序
     *
     * @param dtoList 应用Id的列表
     * @return R
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑常用应用的排列", notes = "保存后会按照传入的列表从前到后排序存入后端")
    public R edit(@Valid @RequestBody List<PortalApplicationCenterDto> dtoList) {
        return portalApplicationCenterService.edit(dtoList);
    }

    /**
     * 列表界面显示
     *
     * @param isAll 是否获取全部
     * @return 不同分类以及常用的应用列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "显示出应用列表", notes = "一个分类标签对应多个应用")
    public R<List<ApplicationViewVo>> list(Boolean isAll) {
        return R.ok(portalApplicationCenterService.listApplication(isAll));
    }

    /**
     * 获取前18个常用图标
     *
     * @return {@link R}<{@link List}<{@link ApplicationIconVo}>>
     */
    @GetMapping("/usefulList")
    @ApiOperation(value = "显示前18个常用应用", notes = "门户首页快捷应用使用")
    public R<List<ApplicationIconVo>> usefulList() {
        return R.ok(portalApplicationCenterService.usefulList());
    }


    /**
     * 应用中心模糊查询
     *
     * @param content 查询内容
     * @return 应用列表
     */
    @GetMapping("/like")
    @ApiOperation(value = "搜索框应用模糊查询")
    public R like(String content) {
        return R.ok(portalApplicationCenterService.queryAppByLike(content));
    }


}
