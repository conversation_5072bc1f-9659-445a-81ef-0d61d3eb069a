package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容管理 Vo类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsContentVo {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 上架状态 字典id
     */
    private Integer status;

    /**
     * 上架状态 字典值
     */
    private String statusTxt;

    /**
     * 发布者id
     */
    private Long publisherId;

    /**
     * 发布者名称
     */
    private String publisherName;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime publishTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 业务分类 字典id
     * {@link com.gok.pboot.portal.enums.BusinessTypeEnum}
     */
    private Integer businessType;

    /**
     * 业务分类 字典值
     */
    private String businessTypeTxt;

    /**
     * 内容类型 字典id
     * {@link com.gok.pboot.portal.enums.ContentTypeEnum}
     */
    private Integer contentType;

    /**
     * 内容类型 字典值
     */
    private String contentTypeTxt;

    /**
     * 内容类别
     * {@link com.gok.pboot.portal.enums.ContentCategoryEnum}
     */
    private Integer contentCategory;

    /**
     * 内容类别 字典值
     */
    private String contentCategoryTxt;

    /**
     * 链接地址
     */
    private String linkAddress;

    /**
     * 关联内容id
     */
    private Long relationContentId;

    /**
     * 正文
     */
    private String contentText;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 封面图url
     */
    private String coverImageUrl;

    /**
     * 展示标记 0-展示 1-不展示
     */
    private Integer showFlag;

    /**
     * 文件附件信息集合
     * {@link List}<{@link CmsContentFileVo}>>
     */
    private List<CmsContentFileVo> fileList;

    /**
     * 关联内容展示标记 0-展示 1-不展示
     */
    private Integer relationShowFlag;

    /**
     * 创建用户名称
     */
    private String createUserName;

}
