package com.gok.pboot.portal.config;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 * 华为云VOD配置
 *
 * @Description   :  华为云VOD配置
 * <AUTHOR>  luoyq
 * @date          :  2023/8/7 16:28
 */
@Component
@Slf4j
@Data
public class HwYunVodConfig {

    @Value("${vod.enable-type}")
    private String enableType;

    @Value("${vod.hwyun.accessKeyId}")
    private String accessKeyId;
    @Value("${vod.hwyun.accessKeySecret}")
    private String accessKeySecret;
    @Value("${vod.hwyun.endpoint}")
    private String endpoint;
    @Value("${vod.hwyun.region}")
    private String region;
    @Value("${vod.hwyun.userId}")
    private String userId;
    @Value("${vod.hwyun.projectId}")
    private String projectId;
    @Value("${vod.hwyun.categoryId}")
    private String categoryId;
    @Value("${vod.hwyun.templateGroupName}")
    private String templateGroupName;
    @Value("${vod.hwyun.vodPcUrl}")
    private String vodPcUrl;
    @Value("${vod.hwyun.vodAppUrl}")
    private String vodAppUrl;
}
