package com.gok.pboot.portal.dto;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import com.gok.pboot.common.core.validation.annotation.LongVerify;
import com.gok.pboot.common.core.validation.annotation.StringVerify;
import com.gok.pboot.portal.db.entity.PortalHrmResource;
import com.gok.pboot.portal.enums.OperationTypeEnum;
import lombok.Data;

/**
 * 内容操作记录新增 Dto类
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
public class CmsOperationLogSaveDto {

    /**
     * 内容id
     */
    @LongVerify(name = "内容id", required = true)
    private Long contentId;

    /**
     * 部门id
     */
    @LongVerify(name = "部门id", required = true)
    private Long deptId;

    /**
     * 部门名称
     */
    @StringVerify(name = "部门名称", maxLen = 32, required = true)
    private String deptName;

    /**
     * 用户id
     */
    @LongVerify(name = "用户id", required = true)
    private Long userId;

    /**
     * 用户名称
     */
    @StringVerify(name = "用户名称", maxLen = 32, required = true)
    private String userName;

    /**
     * 操作类型（0创建、1编辑、2删除、3上架、4下架）
     * {@link com.gok.pboot.portal.enums.OperationTypeEnum}
     */
    @IntegerVerify(name = "操作类型", min = 0, max = 4, required = true)
    private Integer operationType;

    /**
     * 操作内容
     */
    @StringVerify(name = "操作内容", maxLen = 50, required = true)
    private String operationContent;

    /**
     * 构建 DTO
     *
     * @param portalHrmResource 门户 HRM 资源
     * @param operationType     操作类型
     * @param contentId         内容 ID
     * @param operationContent  操作内容
     */
    public void buildDto(PortalHrmResource portalHrmResource, OperationTypeEnum operationType,
                         Long contentId, String operationContent) {
        this.setUserId(portalHrmResource.getId());
        this.setUserName(portalHrmResource.getAliasName());
        this.setDeptId(portalHrmResource.getDeptId());
        this.setOperationType(operationType.getValue());
        this.setContentId(contentId);
        this.setOperationContent(operationContent);
        String userDeptName;
        if (StringUtils.isNotEmpty(portalHrmResource.getThirdDeptName())) {
            userDeptName = portalHrmResource.getThirdDeptName();
        } else if (StringUtils.isNotEmpty(portalHrmResource.getSecondDeptName())) {
            userDeptName = portalHrmResource.getSecondDeptName();
        } else {
            userDeptName = portalHrmResource.getFirstDeptName();
        }
        this.setDeptName(userDeptName);
    }

}
