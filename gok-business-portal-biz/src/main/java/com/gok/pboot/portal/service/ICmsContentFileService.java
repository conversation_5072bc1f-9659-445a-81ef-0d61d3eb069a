package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.portal.db.entity.CmsContentFile;
import com.gok.pboot.portal.dto.CmsContentFileUploadDto;
import com.gok.pboot.portal.vo.CmsContentFileVo;

import java.util.List;

/**
 * <p>
 * 内容附件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
public interface ICmsContentFileService extends IService<CmsContentFile> {

    /**
     * 根据contentId分页查询内容附件
     *
     * @param page      分页请求
     * @param contentId 内容id
     * @return 分页请求响应
     */
    Page<CmsContentFileVo> findPage(Page page, Long contentId);

    /**
     * 批量上传附件
     * 使用全量覆盖方式
     *
     * @param request 附件上传请求
     * @return 新增内容附件id集合
     */
    List<Long> batchUpload(CmsContentFileUploadDto request);

    /**
     * 根据内容id集合批量获取内容附件集合
     *
     * @param contentIds 内容id集合[]
     * @return 内容附件Vo类集合[]
     */
    List<CmsContentFileVo> findByContentIdList(List<Long> contentIds);

}
