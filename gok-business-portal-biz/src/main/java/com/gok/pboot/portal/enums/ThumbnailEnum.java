package com.gok.pboot.portal.enums;

import lombok.Getter;

/**
 * 图片压缩处理
 * 显示的是二倍图
 */
@Getter
public enum ThumbnailEnum {

    /**
     * 1、新闻资讯
     */
    COVER_IMAGE_NEWS_INFO("148_104", "w:148 h:104"),

    /**
     * 2、门户图片
     */
    PORTAL_IMAGE("1545_302", "w:1545 h:302"),

    /**
     * 3、人员图片
     */
    PERSON_IMAGE("370_285", "w:370 h:285");

    private final String value;

    private final String name;


    ThumbnailEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getValue(String name) {
        ThumbnailEnum[] thumbnailEnums = values();
        for (ThumbnailEnum thumbnailEnum : thumbnailEnums) {
            if (thumbnailEnum.getName().equals(name)) {
                return thumbnailEnum.getValue();
            }
        }
        return null;
    }

    public static String getName(String value) {
        ThumbnailEnum[] thumbnailEnums = values();
        for (ThumbnailEnum thumbnailEnum : thumbnailEnums) {
            if (thumbnailEnum.getValue().equals(value)) {
                return thumbnailEnum.getName();
            }
        }
        return null;
    }
}
