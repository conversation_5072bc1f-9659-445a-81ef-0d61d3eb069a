package com.gok.pboot.portal.dto;

import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 项目反馈保存dto
 *
 * <AUTHOR>
 * @date 2023/08/28
 */
@Data
public class ProjectFeedbackSaveDto {

    /**
     * 反馈类型（0:优化建议、1：功能异常、2:其他）
     */
    @NotNull(message = "type 不能为空")
    @IntegerVerify(name = "type",max = 10,min = 0)
    private Integer type;

    /**
     * 应用Id
     */
    @NotNull(message = "applicationId 不能为空")
    private Long applicationId;
    /**
     * 应用名字
     */
    @NotNull(message = "applicationName 不能为空")
    private String applicationName;

    /**
     * 反馈内容
     */
    @NotNull(message = "message 不能为空")
    @Size(max = 300, message = "文本长度不能超过300个字符")
    private String content;

    /**
     * 文件ids，多个文件用,隔开
     */
    private String fileIds;
}
