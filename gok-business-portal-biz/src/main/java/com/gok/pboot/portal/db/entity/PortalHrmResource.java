package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 首页-个人信息（从ehr同步）
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("portal_hrmresource")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "首页-个人信息")
public class PortalHrmResource extends Model<PortalHrmResource> {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String aliasName;

    /**
     * oa部门id
     */
    @ApiModelProperty(value = "oa部门id")
    private Long deptId;

    /**
     * 一级部门
     */
    @ApiModelProperty(value = "一级部门")
    private String firstDeptName;

    /**
     * 二级部门
     */
    @ApiModelProperty(value = "二级部门")
    private String secondDeptName;

    /**
     * 三级部门
     */
    @ApiModelProperty(value = "三级部门")
    private String thirdDeptName;

    /**
    * oa工号
    */
    @ApiModelProperty(value = "oa工号")
    private String workCode;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /**
     * 入职时间
     */
    @ApiModelProperty(value = "入职时间")
    private Date startDate;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String job;

    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    private Integer positionLevel;

    /**
     * 职级序列
     */
    @ApiModelProperty(value = "职级序列")
    private Integer positionLevelSq;

}