package com.gok.pboot.portal.enums;

import lombok.Getter;

/**
 * 反馈排序顺序枚举
 *
 * <AUTHOR>
 * @date 4/9/2023
 */
@Getter
public enum FeedbackSortOrderEnum {
    /**
     * 0 待处理
     */
    CONFIRM(0, "待处理"),

    /**
     * 1 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 2 已处理
     */
    PROCESSED(2, "已处理"),

    /**
     * 3 已解决
     */
    RESOLVED(3, "已解决"),

    /**
     * 4 搁置
     */
    SHELVE(4, "搁置"),

    /**
     * 5 取消
     */
    CANCEL(5, "取消"),

    /**
     * 6 已关闭
     */
    CLOSED(6, "已关闭");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FeedbackSortOrderEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按值获取名称
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String getNameByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (FeedbackSortOrderEnum feedbackSortOrderEnum : FeedbackSortOrderEnum.values()) {
            if (feedbackSortOrderEnum.getValue().equals(value)) {
                return feedbackSortOrderEnum.getName();
            }
        }
        return null;
    }
}
