package com.gok.pboot.portal.db.mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.CmsContent;
import com.gok.pboot.portal.dto.CmsContentDto;
import com.gok.pboot.portal.dto.CmsContentEditCoverUrlDto;
import com.gok.pboot.portal.vo.CmsSortCountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 内容管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Mapper
public interface CmsContentMapper extends BaseMapper<CmsContent> {

    /**
     * 分页查询数据库数据
     *
     * @param page    分页请求
     * @param request 查询请求
     * @return 内容信息分页响应
     */
    Page<CmsContent> findPage(Page page, @Param("query") CmsContentDto request);

    /**
     * 根据权限分页查询数据库数据
     *
     * @param page       分页请求
     * @param request    查询请求
     * @param userId     用户 ID
     * @param deptIdList 部门 ID 列表
     * @return 内容信息分页响应
     */
    Page<CmsContent> findPageByAuth(Page page, @Param("query") CmsContentDto request,@Param("userId") Long userId,@Param("deptIdList") List<Long> deptIdList) ;


    /**
     * 根据id查询内容信息
     *
     * @param idList  内容id集合
     * @param request 查询请求
     * @return 内容信息集合[]
     */
    List<CmsContent> findByIdList(@NotNull @Param("idList") List<Long> idList, @Param("query") CmsContentDto request);

    /**
     * 根据id编辑内容上下架状态
     *
     * @param id         主键id
     * @param status     上下架状态
     * @param updateBy   更新者
     * @param updateTime 更新时间
     */
    void updateStatusById(@Param("id") Long id,
                          @Param("status") Integer status,
                          @Param("updateBy") String updateBy,
                          @Param("updateTime") String updateTime);

    /**
     * 门户首页-查询培训文件
     *
     * @param page 页
     * @return {@link Page}<{@link CmsContent}>
     */
    Page<CmsContent> findTrainingPlan(Page page);

    /**
     * 根据内容id集合进行逻辑删除
     *
     * @param idList 内容id集合[]
     * @return int
     */
    int logicDeleteByIdList(@Param("idList") List<Long> idList);

    /**
     * 根据内容id集合批量变更关联关系
     *
     * @param idList 内容id集合
     */
    void updateRelationByIdList(@Param("idList") List<Long> idList);

    /**
     * 批量修改封面url
     *
     * @param coverUrlDtoList 封面 URL DTO 列表
     */
    void updateCoverUrl(@Param("coverUrlDtoList") List<CmsContentEditCoverUrlDto> coverUrlDtoList);

    /**
     * 修改视频封面url
     *
     * @param id 视频id
     * @param coverImageUrl 视频封面
     */
    void updateCoverUrlByKey(@Param("id") Long id, @Param("coverImageUrl") String coverImageUrl);

    /**
     * 根据id编辑是否展示标记位
     *
     * @param id         主键id
     * @param showFlag   是否展示标记位
     * @param updateBy   更新者
     * @param updateTime 更新时间
     */
    void updateShowFlagById(@Param("id") Long id,
                            @Param("showFlag") Integer showFlag,
                            @Param("updateBy") String updateBy,
                            @Param("updateTime") String updateTime);


    /**
     * 获取发布时间五分钟且状态为未发送的内容
     *
     * @return {@link List}<{@link CmsContent}>
     */
    List<CmsContent> getByPublishTime();

    /**
     * 更改发送状态
     *
     * @param idList ID 列表
     */
    void changeSendStatus(@Param("idList") ArrayList<Long> idList);

    /**
     * 查找公文分类计数
     *
     * @param userId     用户 ID
     * @param deptIdList 部门 ID 列表
     * @return {@link List}<{@link CmsSortCountVo}>
     */
    List<CmsSortCountVo> findSortCount(@Param("userId") Long userId,@Param("deptIdList") List<Long> deptIdList);
}
