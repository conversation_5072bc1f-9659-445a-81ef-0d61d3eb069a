package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.PortalBackground;
import com.gok.pboot.portal.dto.PortalBackgroundDto;
import com.gok.pboot.portal.vo.PortalBackgroundVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 门户背景管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Mapper
public interface PortalBackgroundMapper extends BaseMapper<PortalBackground> {

    /**
     * 分页查询数据库数据
     *
     * @param page
     * @param request
     * @return {@link Page}<{@link PortalBackgroundVo}>
     */
    Page<PortalBackground> findPage(Page page, @Param("query") PortalBackgroundDto request);

    /**
     * 查询用户适配背景
     *
     * @param birthday    个人生日适用类型
     * @param anniversary 入职周年适用类型
     * @param defaultFlag 默认标志位
     * @return {@link PortalBackground}
     */
    PortalBackground find(@Param("birthday") Integer birthday,
                          @Param("anniversary") Integer anniversary,
                          @Param("defaultFlag") Integer defaultFlag);

    /**
     * 根据id编辑内容上下架状态
     *
     * @param id         主键id
     * @param status     上下架状态
     * @param updateBy   更新人
     * @param updateTime 更新时间
     */
    void updateStatusById(@Param("id") Long id,
                          @Param("status") Integer status,
                          @Param("updateBy") String updateBy,
                          @Param("updateTime") String updateTime);

    /**
     * 根据内容id集合进行逻辑删除
     *
     * @param idList 内容id集合[]
     * @return
     */
    int logicDeleteByIdList(@Param("idList") List<Long> idList);

    PortalBackground findNormal(@Param("weight") Integer weight);

}
