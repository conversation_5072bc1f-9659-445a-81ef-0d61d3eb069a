package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 员工状态 Enum
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Getter
public enum UserStatusEnum implements ValueEnum<Integer> {

    /**
     * 试用
     */
    TEST(0, "试用"),

    /**
     * 正式
     */
    FORMAL(1, "正式"),

    /**
     * 临时
     */
    TEMPORARY(2, "临时"),

    /**
     * 试用延期
     */
    TEST_DELAY(3, "试用延期"),

    /**
     * 解聘
     */
    DISMISS(4, "解聘"),

    /**
     * 离职
     */
    DEPART(5, "离职"),

    /**
     * 退休
     */
    RETIRE(6, "退休"),

    /**
     * 无效
     */
    INVALID(7, "无效");

    private Integer value;

    private String name;

    public static Integer[] valid = {0, 1, 2, 3};

    UserStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
