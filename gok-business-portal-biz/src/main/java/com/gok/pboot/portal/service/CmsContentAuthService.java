package com.gok.pboot.portal.service;

import com.gok.pboot.portal.db.entity.CmsContentAuth;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * CMS内容认证服务
 *
 * <AUTHOR>
 * @description 针对表【cms_content_auth(内容权限表)】的数据库操作Service
 * @createDate 2024-01-12 09:20:36
 * @date 2024/01/12
 */
public interface CmsContentAuthService extends IService<CmsContentAuth> {


    /**
     * 按对象类型批量保存
     *
     * @param contentId  内容编号
     * @param userIdList 用户 ID 列表
     * @param obj        对象
     */
    void batchSaveByObj(Integer obj,Long contentId, List<Long> userIdList);

    /**
     * 按 Content ID 批量删除
     *
     * @param contentId 内容 ID
     */
    void batchDeleteByContentId(Long contentId);

    /**
     * 按 ContentIdList 批量删除
     *
     * @param contentIdList Content ID 列表
     */
    void batchDeleteByContentIdList(List<Long> contentIdList);


    /**
     * 按内容 ID 获取 ID 列表
     *
     * @param sendObj   发送 OBJ
     * @param contentId 内容 ID
     * @return {@link List}<{@link Long}>
     */
    List<Long> getIdListByContentId(Integer sendObj, Long contentId);

    /**
     * 是按 CMS ID 进行身份验证
     *

     * @param contentId 内容 ID
     * @param sendObj   发送 OBJ
     * @return {@link Boolean}
     */
    Boolean isAuthByCmsId( Long contentId, Integer sendObj);
}
