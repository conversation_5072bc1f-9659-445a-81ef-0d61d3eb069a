package com.gok.pboot.portal.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalApplicationManage;
import com.gok.pboot.portal.dto.PortalApplicationManagePageDto;
import com.gok.pboot.portal.dto.PortalApplicationManageSaveOrUpdateDto;
import com.gok.pboot.portal.vo.PortalApplicationManageBoxVo;
import com.gok.pboot.portal.vo.PortalApplicationManageVo;

import java.util.List;


/**
 * * <p>
 * * 应用管理表 服务类
 * * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IPortalApplicationManageService extends IService<PortalApplicationManage> {

    /**
     * 根据categoryId 获取数据库对应管理个数
     *
     * @param id categoryId
     * @return 个数
     */
    Integer queryByCategoryId(Long id);

    /**
     * 分页模糊查询
     *
     * @param page    分页
     * @param pageDto 模糊查询条件
     * @return R
     */
    R<Page<PortalApplicationManageVo>> queryAllManage(Page page, PortalApplicationManagePageDto pageDto);

    /**
     * 上下架
     *
     * @param id 应用管理Id
     * @return R
     */
    R onOrOff(Long id);


    /**
     * 新增应用管理
     *
     * @param dto 实体类
     * @return R
     */
    R saveDto(PortalApplicationManageSaveOrUpdateDto dto);


    /**
     * 修改应用管理
     *
     * @param dto 实体类
     * @return R
     */
    R updateDto(PortalApplicationManageSaveOrUpdateDto dto);

    /**
     * 下架后的删除操作
     *
     * @param id 应用Id
     * @return R
     */
    R remove(Long id);

    /**
     * 获取全部未上架的应用
     *
     * @return 应用列表
     */
    List<PortalApplicationManageBoxVo> offShelfList();

    /**
     * 根据id获取当前应用信息
     *
     * @param id 应用id
     * @return 应用详情信息
     */
    PortalApplicationManageVo getOne(Long id);

}
