package com.gok.pboot.portal.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单管理--详情的VO
 *
 * <AUTHOR>
 * @since 2023-08-24
 */

@Data
public class WorkOrderDetailVo {

    /**
     * 反馈人
     */
    private String feedbackCreateBy;

    /**
     * 部门
     */
    private String dept;

    /**
     * 处理状态
     */
    private Integer handlingSituation;

    /**
     * 处理状态中文
     */
    private String handlingSituationStr;

    /**
     * 跟进人ID
     */
    private Long followedWorkerId;

    /**
     * 跟进人
     */
    private String followedWorker;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈类型中文
     */
    private String typeStr;

    /**
     * 反馈时间
     */
    private LocalDateTime feedbackCreateTime;

    /**
     * 系统
     */
    private String applicationName;

    /**
     * 反馈相关图片
     */
    private List<String> feedbackFileIds;

    /**
     * 批转信息
     */
    private List<ProjectFeedbackLogVo> logVos;

    /**
     * 回复内容
     */
    private String responseContent;

    /**
     * 回复相关图片
     */
    private List<String> responseFileIds;

    /**
     * 回复时间
     */
    private LocalDateTime responseCreateTime;

    /**
     * 服务态度
     */
    private Integer serviceAttitude;

    /**
     * 处理效果
     */
    private Integer serviceEffectiveness;

    /**
     * 处理效率
     */
    private Integer serviceEfficiency;

    /**
     * 服务感受
     */
    private String serviceFeel;


}

