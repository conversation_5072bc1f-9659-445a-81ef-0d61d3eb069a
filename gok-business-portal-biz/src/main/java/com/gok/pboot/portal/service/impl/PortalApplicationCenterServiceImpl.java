package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysOauthClientDetailsVO;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.PortalApplicationCategory;
import com.gok.pboot.portal.db.entity.PortalApplicationCenter;
import com.gok.pboot.portal.db.entity.PortalApplicationManage;
import com.gok.pboot.portal.db.mapper.PortalApplicationCenterMapper;
import com.gok.pboot.portal.db.mapper.PortalApplicationManageMapper;
import com.gok.pboot.portal.dto.PortalApplicationCenterDto;
import com.gok.pboot.portal.enums.LaunchStatusEnum;
import com.gok.pboot.portal.service.IPortalApplicationCategoryService;
import com.gok.pboot.portal.service.IPortalApplicationCenterService;
import com.gok.pboot.portal.service.IPortalApplicationManageService;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.ApplicationViewVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-02
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PortalApplicationCenterServiceImpl extends ServiceImpl<PortalApplicationCenterMapper, PortalApplicationCenter> implements IPortalApplicationCenterService {

    @Autowired
    private IPortalApplicationCategoryService portalApplicationCategoryService;

    @Autowired
    private IPortalApplicationManageService portalApplicationManageService;

    @Autowired
    private PortalApplicationCenterMapper portalApplicationCenterMapper;

    @Autowired
    private PortalApplicationManageMapper portalApplicationManageMapper;

    @Resource
    private CdnImgUtils cdnImgUtils;


    @Resource
    private RemoteOutService remoteOutService;

    @Value("${appId}")
    private Long appId;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R edit(List<PortalApplicationCenterDto> dtoList) {
        //删除该用户旧的全部常用应用列表
        LambdaQueryWrapper<PortalApplicationCenter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PortalApplicationCenter::getUserId, UserUtils.getUser().getId());
        this.remove(queryWrapper);

        int sortId = 1;
        List<PortalApplicationCenter> portalApplicationCenterList = new ArrayList<>();
        for (PortalApplicationCenterDto dto : dtoList) {
            PortalApplicationCenter portalApplicationCenter = new PortalApplicationCenter();
            //复制信息
            BeanUtil.copyProperties(dto, portalApplicationCenter);

            //封装其他想象
            portalApplicationCenter.setSortOrder(sortId++);
            portalApplicationCenter.setUserId(UserUtils.getUser().getId());
            portalApplicationCenter.setCreateBy(UserUtils.getUser().getName());
            portalApplicationCenter.setCreateTime(LocalDateTime.now());
            portalApplicationCenter.setTenantId(UserUtils.getUser().getTenantId());

            portalApplicationCenterList.add(portalApplicationCenter);
        }
        this.saveBatch(portalApplicationCenterList);

        return R.ok("编辑成功");
    }

    @Override
    public List<ApplicationViewVo> listApplication(Boolean isAll) {
        isAll = isAll != null && isAll;
        //1.获取有权限的应用Id
        List<Long> ids = rightAppVoToIds();
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }

        //2.初始化返回类
        List<ApplicationViewVo> viewVoList = new ArrayList<>();
        List<SysOauthClientDetailsVO> data = remoteOutService.getAppListByUserId(UserUtils.getUser().getId(), appId).getData();

        //3.获取全部的分类
        LambdaQueryWrapper<PortalApplicationCategory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PortalApplicationCategory::getDelFlag, 0);
        lambdaQueryWrapper.orderByAsc(PortalApplicationCategory::getSortOrder);
        List<PortalApplicationCategory> categoryList = portalApplicationCategoryService.list(lambdaQueryWrapper);

        //4.封装常用应用列表
        List<ApplicationIconVo> applicationIconVos =
                portalApplicationCenterMapper.queryUsefulApplication(UserUtils.getUser().getId());
        List<Long> usefulApplicationIdList = CollUtil.isNotEmpty(applicationIconVos)
                ? applicationIconVos.stream().map(ApplicationIconVo::getBelongApplicationId).collect(Collectors.toList())
                : new ArrayList<>();
        // 封装门户的跳转地址
        List<ApplicationIconVo> setMhLinkAddress = setMhLinkAddress(applicationIconVos, data);
        ApplicationViewVo usefulView = new ApplicationViewVo(0L, "常用应用", setMhLinkAddress);
        viewVoList.add(usefulView);

        //5.循环封装不同分类下的应用
        for (PortalApplicationCategory portalApplicationCategory : categoryList) {
            ApplicationViewVo viewVo = new ApplicationViewVo();
            //5.0封装id
            viewVo.setCategoryId(portalApplicationCategory.getId());
            //5.1封装分类名称
            viewVo.setCategoryName(portalApplicationCategory.getCategoryName());
            //5.2封装分类下的list
            List<Long> categoryAppIds;
            if (!isAll){
                categoryAppIds = CollUtil.isNotEmpty(usefulApplicationIdList)
                        ? ids.stream().filter(i -> !usefulApplicationIdList.contains(i)).collect(Collectors.toList())
                        : ids;
            }else {
                categoryAppIds = ids;
            }
            //常用应用包含所有应用则使所属应用列表为空
            List<ApplicationIconVo> applicationIcons =
                    CollUtil.isEmpty(categoryAppIds)?
                            new ArrayList<>():portalApplicationCenterMapper.queryNoUsefulByCategoryId(portalApplicationCategory.getId(), categoryAppIds);

            List<ApplicationIconVo> mhLinkAddress = setMhLinkAddress(applicationIcons, data);
            viewVo.setApplicationIconVoList(mhLinkAddress);
            viewVoList.add(viewVo);
        }

        return viewVoList;
    }

    @Override
    public List<ApplicationIconVo> usefulList() {
        List<ApplicationIconVo> allIcon = portalApplicationCenterMapper.queryUsefulApplication(UserUtils.getUser().getId());

        List<SysOauthClientDetailsVO> data = remoteOutService.getAppListByUserId(UserUtils.getUser().getId(), appId).getData();
        //为空则取六个
        if (allIcon.isEmpty()) {
            allIcon = this.queryRightApplication();
        }
        if (allIcon.size() > 18) {
            allIcon = allIcon.subList(0, 18);
        }

        return setMhLinkAddress(allIcon, data);
    }

    @Override
    public List<ApplicationIconVo> queryAppByLike(String content) {
        List<Long> ids = rightAppVoToIds();
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        List<ApplicationIconVo> applicationIconVos = portalApplicationManageMapper.queryRightApplicationByLike(ids, content);

        for (ApplicationIconVo vo:applicationIconVos){
            vo.setFileUrl(cdnImgUtils.createHttpUrl(vo.getFileUrl()));
        }
        return applicationIconVos;
    }

    /**
     * 排除常用的应用，再根据应用分类查询
     *
     * @param list       常用的应用列表
     * @param categoryId 应用分类id
     * @return 某个分类下的非常用应用
     */
    private List<ApplicationIconVo> queryIconListWithoutUseful(List<PortalApplicationCenter> list, Long categoryId) {
        LambdaQueryWrapper<PortalApplicationManage> queryWrapper = new LambdaQueryWrapper<>();
        //数据库查询需要的信息
        queryWrapper.eq(PortalApplicationManage::getDelFlag, 0);
        queryWrapper.eq(PortalApplicationManage::getStatus, 0);
        queryWrapper.eq(PortalApplicationManage::getCategoryId, categoryId);

        for (PortalApplicationCenter portalApplicationCenter : list) {

            queryWrapper.ne(PortalApplicationManage::getId, portalApplicationCenter.getApplicationId());
        }
        queryWrapper.orderByAsc(PortalApplicationManage::getWeight);
        List<PortalApplicationManage> manageList = portalApplicationManageService.list(queryWrapper);

        //封装返回结果
        List<ApplicationIconVo> resList = new ArrayList<>();
        for (PortalApplicationManage portalApplicationManage : manageList) {
            ApplicationIconVo applicationIconVo = new ApplicationIconVo();
            BeanUtil.copyProperties(portalApplicationManage, applicationIconVo);
            applicationIconVo.setApplicationId(portalApplicationManage.getId());
            resList.add(applicationIconVo);
        }
        return resList;
    }

    /**
     * 获取当前用户有权限应用的ico
     *
     * @return
     */
    private List<ApplicationIconVo> queryRightApplication() {
        List<Long> ids = rightAppVoToIds();
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return portalApplicationManageMapper.queryRightApplicationByIds(ids);
    }

    /**
     * 将应用VO提取出Id
     *
     * @return Id列表
     */
    private List<Long> rightAppVoToIds() {
        R<List<SysOauthClientDetailsVO>> appListByUserId = remoteOutService.getAppListByUserId(UserUtils.getUser().getId(), appId);
        List<SysOauthClientDetailsVO> rightApp = appListByUserId.getData();
        List<Long> ids = new ArrayList<>();
        for (SysOauthClientDetailsVO vo : rightApp) {
            //判断是否下架
            if (Optional.ofNullable(vo).isPresent()
                    && LaunchStatusEnum.LAUNCH.getValue().equals(Integer.valueOf(vo.getStatus()))) {
                ids.add(vo.getId());
            }
        }
        return ids;
    }

    /**
     * 给门户的应用赋予中台的地址
     * @param applicationIconVoList 门户的应用列表
     * @param sysOauthClientDetailsVOList 中台的应用列表
     * @return 修改后的门户应用列表
     */
    private List<ApplicationIconVo> setMhLinkAddress(List<ApplicationIconVo> applicationIconVoList,List<SysOauthClientDetailsVO>sysOauthClientDetailsVOList){
        boolean flag = true;
        if (applicationIconVoList.isEmpty()){
            return null;
        }
        Iterator<ApplicationIconVo> iterator = applicationIconVoList.iterator();

        while (iterator.hasNext()) {
            ApplicationIconVo iconVo = iterator.next();

            iconVo.setFileUrl(cdnImgUtils.createHttpUrl(iconVo.getFileUrl()));

            flag = true;

            for (SysOauthClientDetailsVO detailsVO : sysOauthClientDetailsVOList) {
                if (iconVo.getBelongApplicationId() != null && iconVo.getBelongApplicationId().equals(detailsVO.getId())) {
                    iconVo.setLinkAddress(detailsVO.getRedirectUrl());
                    flag = false;
                    break;
                }
            }

            if (flag) {
                // 如果含有非权限的应用，则使用迭代器的 remove 方法移除
                iterator.remove();
            }
        }

        return applicationIconVoList;
    }
}
