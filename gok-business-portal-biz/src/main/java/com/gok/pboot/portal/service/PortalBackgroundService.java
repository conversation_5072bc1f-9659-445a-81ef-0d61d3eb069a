package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalBackground;
import com.gok.pboot.portal.dto.PortalBackgroundDto;
import com.gok.pboot.portal.dto.PortalBackgroundSaveOrUpdateDto;
import com.gok.pboot.portal.vo.PortalBackgroundDetailVo;
import com.gok.pboot.portal.vo.PortalBackgroundVo;

import java.util.List;

/**
 *  * <p>
 *  * 门户背景管理表 服务类
 *  * </p>
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
public interface PortalBackgroundService extends IService<PortalBackground> {

    /**
     * 分页查询背景列表
     *
     * @param page    分页参数
     * @param request 查询请求
     * @return {@link Page}<{@link PortalBackgroundVo}>
     */
    Page<PortalBackgroundVo> findPage(Page page, PortalBackgroundDto request);

    /**
     * 查询背景详情
     *
     * @param backgroundId 背景id
     * @return {@link PortalBackgroundDetailVo}
     */
    PortalBackgroundDetailVo findDetail(Long backgroundId);

    /**
     * 查询用户适配背景
     *
     * @return {@link PortalBackgroundVo}
     */
    R<PortalBackgroundVo> find();

    /**
     * 新增门户背景
     *
     * @param request 新增请求
     * @return {@link Long}
     */
    Long save(PortalBackgroundSaveOrUpdateDto request);

    /**
     * 更新门户背景
     *
     * @param request 更新请求
     * @return {@link Long}
     */
    Long update(PortalBackgroundSaveOrUpdateDto request);

    /**
     * 上下架门户背景
     *
     * @param backgroundId     id
     * @param status 状态
     * @return {@link Long}
     */
    Long updateStatus(Long backgroundId, Integer status);

    /**
     * 批量删除门户背景
     *
     * @param ids 门户背景主键id集合[]
     * @return {@link Long}
     */
    List<Long> batchDelete(List<Long> ids);

}
