package com.gok.pboot.portal.service;

import com.gok.bcp.message.res.BasePageRes;
import com.gok.bcp.message.vo.BcpMessageVO;
import com.gok.pboot.portal.dto.WorkFlowPageDTO;
import com.gok.pboot.portal.vo.MessageVo;
import com.gok.pboot.portal.vo.WorkFlowTaskVo;

import java.util.List;

/**
 * 移动端-消息列表
 *
 * <AUTHOR>
 * @description 移动端-消息列表
 * @menu 移动端-消息列表
 * @date 2024/03/01
 */
public interface IPortalMessageService {

    /**
     * 移动端-消息列表
     *
     * @return {@link List}<{@link MessageVo}>
     */
    List<MessageVo> getMessageGroup();

    /**
     * 移动端-单平台消息列表
     *
     * @param source  {@link String} 项目code
     * @param current {@link Long} 当前页
     * @param size    {@link Long} 分页条数
     * @return {@link List}<{@link BcpMessageVO}>
     */
    BasePageRes<BcpMessageVO> getMessageByProject(String source, Long current, Long size);

    /**
     * 代办（任务/流程）列表
     *
     * @return {@link List}<{@link WorkFlowTaskVo}>
     */
    List<WorkFlowTaskVo> getTaskGroup();

    /**
     * 移动端-单平台代办列表
     *
     * @param workFlowPageDTO {@link WorkFlowPageDTO} 分页参数封装
     * @return {@link BasePageRes}<{@link WorkFlowTaskVo}>
     */
    BasePageRes<WorkFlowTaskVo> getTaskByPage(WorkFlowPageDTO workFlowPageDTO);
}
