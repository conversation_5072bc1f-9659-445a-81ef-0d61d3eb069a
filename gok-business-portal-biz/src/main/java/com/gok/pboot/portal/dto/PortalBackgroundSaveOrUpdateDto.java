package com.gok.pboot.portal.dto;

import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import com.gok.pboot.common.core.validation.annotation.LongVerify;
import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 门户背景管理 Dto
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Data
public class PortalBackgroundSaveOrUpdateDto {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    @StringVerify(name = "标题", maxLen = 50, required = true)
    private String title;

    /**
     * 适用日期开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    /**
     * 适用日期结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    /**
     * 个人生日适用类型
     * {@link com.gok.pboot.portal.enums.SuitTypeEnum}
     */
    @IntegerVerify(name = "个人生日适用类型", min = 0, max = 1)
    private Integer birthday;

    /**
     * 入职周年适用类型
     * {@link com.gok.pboot.portal.enums.SuitTypeEnum}
     */
    @IntegerVerify(name = "入职周年适用类型", min = 0, max = 1)
    private Integer anniversary;

    /**
     * 上架状态（0已上架、1未上架）
     * {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     */
    @IntegerVerify(name = "上架状态", min = 0, max = 1, required = true)
    private Integer status;

    /**
     * 门户背景文件id
     */
    @LongVerify(name = "门户背景文件id", required = true)
    private Long portalFileId;

    /**
     * 个人背景文件id
     */
    private Long personFileId;

    /**
     * 权重
     */
    @IntegerVerify(name = "权重", min = 0, max = 99, required = true)
    private Integer weight;

    /**
     * 默认标志位
     * {@link com.gok.pboot.portal.enums.BackgroundTypeEnum}
     */
    private Integer defaultFlag;

    /**
     * 个人背景文件id
     */
    @LongVerify(name = "门户背景文件id")
    private Long appFileId;

}
