package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.message.dto.BcpMessageCxDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgStatusEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.feign.RemoteMessageService;
import com.gok.bcp.message.res.BasePageRes;
import com.gok.bcp.message.utils.EnumUtils;
import com.gok.bcp.message.vo.BcpMessageVO;
import com.gok.bcp.flowable.task.common.enums.task.TaskStatusEnum;
import com.gok.bcp.flowable.task.feign.RemoteTaskService;
import com.gok.bcp.flowable.task.feign.RemoteWorkflowService;
import com.gok.bcp.flowable.task.req.WorkflowReq;
import com.gok.bcp.flowable.task.vo.TaskPageVo;
import com.gok.bcp.flowable.task.vo.TaskSourceStatusCountVo;
import com.gok.bcp.flowable.task.vo.WorkflowPageVo;
import com.gok.bcp.flowable.task.vo.WorkflowSourceCountVo;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.portal.db.mapper.PortalApplicationManageMapper;
import com.gok.pboot.portal.dto.WorkFlowPageDTO;
import com.gok.pboot.portal.enums.WorkFlowTypeEnum;
import com.gok.pboot.portal.service.IPortalMessageService;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.vo.MessageVo;
import com.gok.pboot.portal.vo.SourceFileUrlVo;
import com.gok.pboot.portal.vo.TaskVo;
import com.gok.pboot.portal.vo.WorkFlowTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 移动端-消息列表
 *
 * <AUTHOR>
 * @description 移动端-消息列表
 * @menu 移动端-消息列表
 * @date 2024/03/01
 */
@Service
@RequiredArgsConstructor
public class PortalMessageServiceImpl implements IPortalMessageService {

    private final RemoteMessageService remoteMessageService;

    private final RemoteTaskService remoteTaskService;

    private final RemoteWorkflowService remoteWorkflowService;

    private final RemoteBcpDictService remoteBcpDictService;

    private final WorkFlowTaskServiceImpl workFlowTaskService;

    private final PortalApplicationManageMapper portalApplicationManageMapper;

    private final CdnImgUtils cdnImgUtils;

    /**
     * 移动端-消息列表
     *
     * @return {@link List}<{@link MessageVo}>
     */
    @Override
    public List<MessageVo> getMessageGroup() {
        // 1、获取中台消息数据
        BcpMessageCxDTO dto = BcpMessageCxDTO.builder()
                .userId(UserUtils.getUser().getId())
                .channel(ChannelEnum.MAIL.getValue())
                .build();
        Map<String, List<BcpMessageVO>> map = remoteMessageService
                .getMessageGroup(dto)
                .getData();
        // 2、封装每个平台第一条数据以及未读数量
        List<MessageVo> voList = new ArrayList<>(map.size());
        Map<String, String> sourceFileMap = portalApplicationManageMapper.getFileUrl()
                .stream()
                .collect(Collectors.toMap(SourceFileUrlVo::getSource, SourceFileUrlVo::getFileUrl, (a, b) -> a));
        map.keySet().forEach(key -> {
            if (!key.equals(SourceEnum.MOBILE.getName())) {
                MessageVo messageVO = BeanUtil.copyProperties(map.get(key).get(NumberUtils.INTEGER_ZERO), MessageVo.class);
                // 2.1、未读数量
                long unReadCount = map.get(key)
                        .stream()
                        .filter(m -> MsgStatusEnum.UNREAD.getValue().equals(m.getStatus())).count();
                messageVO.setUnReadCount(unReadCount);
                // 2.2、平台来源标识
                messageVO.setSourceTxt(EnumUtils.getNameByValue(SourceEnum.class, key));
                // 2.3、平台logo
                messageVO.setLogo(cdnImgUtils.createHttpUrl(sourceFileMap.get(key)));
                voList.add(messageVO);
            }
        });
        return voList;
    }

    /**
     * 移动端-单平台消息列表
     *
     * @param source  {@link String} 项目code
     * @param current {@link Long} 当前页
     * @param size    {@link Long} 分页条数
     * @return {@link List}<{@link BcpMessageVO}>
     */
    @Override
    public BasePageRes<BcpMessageVO> getMessageByProject(String source, Long current, Long size) {
        BasePageRes<BcpMessageVO> page = new BasePageRes<>();
        // 1、获取全部数据
        BcpMessageCxDTO dto = BcpMessageCxDTO.builder()
                .userId(UserUtils.getUser().getId())
                .channel(ChannelEnum.MAIL.getValue())
                .build();
        List<BcpMessageVO> voList = remoteMessageService
                .getMessageGroup(dto)
                .getData()
                .get(source);
        if (voList == null || voList.isEmpty()) {
            return page;
        }
        // 2、分页数据
        List<BcpMessageVO> pageList = voList.stream()
                .skip((current - NumberUtils.INTEGER_ONE) * size)
                .limit(size)
                .collect(Collectors.toList());
        // 3、封装分页
        page.setTotal(voList.size());
        page.setList(pageList);
        return page;
    }

    /**
     * 代办（任务/流程）列表
     *
     * @return {@link List}<{@link TaskVo}>
     */
    @Override
    public List<WorkFlowTaskVo> getTaskGroup() {
        Long userId = UserUtils.getUser().getId();
        List<WorkFlowTaskVo> voList = new ArrayList<>();
        // 1、根据当前登录人获取所有代办任务与流程信息
        List<TaskPageVo> taskList = remoteTaskService.getTaskGroupBySource(userId, null).getData();
        List<WorkflowPageVo> workflowPageList = remoteWorkflowService.getFirstWorkflowGroupBySource(userId).getData();
        // 2、任务信息处理
        if (taskList != null) {
            // 2.1、获取所有代办任务信息过滤出仅【未开始、进行中、已延期】的数据
            List<TaskSourceStatusCountVo> list = remoteTaskService.statusCountBySource(userId, null).getData();
            Map<String, Long> unStartedMap = list.stream()
                    .filter(t -> TaskStatusEnum.NOT_STARTED.getValue().equals(t.getStatus()))
                    .collect(Collectors.toMap(TaskSourceStatusCountVo::getSource, TaskSourceStatusCountVo::getCount));
            Map<String, Long> inProgressMap = list.stream()
                    .filter(t -> TaskStatusEnum.IN_PROGRESS.getValue().equals(t.getStatus()))
                    .collect(Collectors.toMap(TaskSourceStatusCountVo::getSource, TaskSourceStatusCountVo::getCount));
            Map<String, Long> delayedMap = list.stream()
                    .filter(t -> TaskStatusEnum.DELAYED.getValue().equals(t.getStatus()))
                    .collect(Collectors.toMap(TaskSourceStatusCountVo::getSource, TaskSourceStatusCountVo::getCount));
            // 2.2、封装未开始信息
            taskList.forEach(t -> {
                WorkFlowTaskVo vo = BeanUtil.copyProperties(t, WorkFlowTaskVo.class);
                vo.setWorkflowTaskType(WorkFlowTypeEnum.TASK.getValue());
                vo.setTaskSource(t.getSource());
                vo.setTaskSourceStr(t.getSourceStr());
                Long unstartedCount = NumberUtils.LONG_ZERO;
                if (unStartedMap.containsKey(t.getSource())) {
                    unstartedCount += unStartedMap.get(t.getSource());
                }
                if (inProgressMap.containsKey(t.getSource())) {
                    unstartedCount += inProgressMap.get(t.getSource());
                }
                if (delayedMap.containsKey(t.getSource())) {
                    unstartedCount += delayedMap.get(t.getSource());
                }
                vo.setUnStartedCount(unstartedCount);
                voList.add(vo);
            });
        }
        // 3、流程信息处理
        if (workflowPageList != null) {
            // 3.1、获取应用来源字典
            Map<String, String> dictMap = remoteBcpDictService.getDictKvList("WORKFLOW_DATA_SOURCE_KEY").getData()
                    .stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));
            // 3.2、获取所有代办流程信息用于封装未处理完个数
            WorkflowReq req = new WorkflowReq();
            req.setUserId(userId);
            Map<String, Long> workflowMap = remoteWorkflowService.countBySource(req)
                    .getData()
                    .stream()
                    .collect(Collectors.toMap(WorkflowSourceCountVo::getSource, WorkflowSourceCountVo::getCount));
            // 3.3、封装代办流程信息
            workflowPageList.forEach(w -> {
                WorkFlowTaskVo vo = BeanUtil.copyProperties(w, WorkFlowTaskVo.class);
                vo.setWorkflowTaskType(WorkFlowTypeEnum.WORKFLOW.getValue());
                vo.setWorkflowSource(w.getSource());
                vo.setWorkflowSourceStr(dictMap.get(w.getSource()));
                vo.setUnStartedCount(workflowMap.get(w.getSource()));
                voList.add(vo);
            });
        }
        return voList;
    }

    /**
     * 移动端-单平台代办列表
     *
     * @param workFlowPageDTO {@link WorkFlowPageDTO} 分页参数封装
     * @return {@link BasePageRes}<{@link WorkFlowTaskVo}>
     */
    @Override
    public BasePageRes<WorkFlowTaskVo> getTaskByPage(WorkFlowPageDTO workFlowPageDTO) {
        Page<WorkFlowTaskVo> data = workFlowTaskService.getRemotePage(workFlowPageDTO).getData();
        BasePageRes<WorkFlowTaskVo> res = new BasePageRes<>();
        res.setList(data.getRecords());
        res.setTotal(data.getTotal());
        return res;
    }

}
