package com.gok.pboot.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.pboot.portal.db.entity.ProjectFeedback;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.portal.dto.*;
import com.gok.pboot.portal.enums.FeedbackOperationTypeEnum;
import com.gok.pboot.portal.vo.*;
import com.gok.pboot.portal.vo.WorkOrderDetailVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface IProjectFeedbackService extends IService<ProjectFeedback> {

    /**
     * 我的反馈
     *
     * @param page    页面
     * @param content 内容
     * @return {@link R}<{@link Page}<{@link ProjectFeedbackVo}>>
     */
    R<Page<ProjectFeedbackVo>> queryFeedbackByCreate(Page page, String content);

    /**
     * 删除项目反馈
     *
     * @param id id
     * @return {@link R}
     */
    R removeProjectFeedback(Long id);

    /**
     * 提交反馈
     *
     * @param dto dto
     * @return {@link R}
     */
    R saveDto(ProjectFeedbackSaveDto dto);


    /**
     * 工单管理--详情界面修改跟进人
     *
     * @param dto 修改dto
     * @return R
     */
    R workOrderDetailUpdate(WorkOrderDetailUpdateDto dto);

    /**
     * 反馈详情
     *
     * @param id id
     * @return {@link R}
     */
    R getDetail(Long id);

    /**
     * 满意度
     *
     * @param projectFeedbackSatisfactionDto 项目反馈满意度dto
     * @return {@link R}
     */
    R satisfaction(ProjectFeedbackSatisfactionDto projectFeedbackSatisfactionDto);

    /**
     * 工单管理--列表
     *
     * @param page          页面
     * @param workOrderPage 工作订单页面
     * @return {@link R}
     */
    R workOrderPage(Page page, WorkOrderPageDto workOrderPage);


    /**
     * 关闭项目反馈
     *
     * @param id id
     * @return {@link R}
     */
    R closeProjectFeedback(Long id);


    /**
     * 模糊查询人员名单
     *
     * @param userName 模糊姓名
     * @return 查询到到结果列表
     */
    R<List<SysUserOutVO>> userLikeList(String userName);


    /**
     * 处理获取禅道关闭的任务
     *
     * @return
     */
    List<UnclosedBugsVo> getUnclosedBugs();

    /**
     * 工单管理通用接口
     *
     * @param id 反馈id
     * @return Vo
     */
    R<WorkOrderVo> showWorkOrder(Long id);


    /**
     * 工单管理通用操作接口
     *
     * @param dto 操作dto
     * @return
     */
    R doWorkOrder(WorkOrderDto dto);

    /**
     * 反馈前台下拉框
     * @return 下拉框列表
     */
    List<ApplicationIconVo> getAppBox();
}
