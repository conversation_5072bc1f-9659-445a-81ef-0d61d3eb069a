package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 应用分类请求Dto
 * <AUTHOR>
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortalApplicationCategoryVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 应用数量
     */
    private Integer applicationCount;

    /**
     * 排序
     */
    private Integer sortOrder;
}
