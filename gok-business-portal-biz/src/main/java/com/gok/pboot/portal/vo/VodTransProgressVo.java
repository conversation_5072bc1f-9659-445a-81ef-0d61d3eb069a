package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VOD Trans Progress VO
 *
 * <AUTHOR>
 * @Description :  视频转码
 * @date 2024/01/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VodTransProgressVo {

    /**
     * 视频key值
     */
    private String videoKey;

    /**
     * 视频封面
     */
    private String videoCover;

    /**
     * 转码进度0-100
     */
    private Long transCode;

}
