package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Locale;

/**
 * 工单管理-反馈列表（后台）VO
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeedbackBackVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型（0:优化建议、1：功能异常、2:其他）
     */
    private Integer type;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 处理情况（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    private Integer handlingSituation;

}
