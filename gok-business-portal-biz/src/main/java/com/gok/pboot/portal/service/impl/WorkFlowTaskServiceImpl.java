package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.flowable.task.common.enums.task.TaskStatusEnum;
import com.gok.bcp.flowable.task.dto.task.TaskStatusSyncDto;
import com.gok.bcp.flowable.task.feign.RemoteTaskService;
import com.gok.bcp.flowable.task.feign.RemoteWorkflowService;
import com.gok.bcp.flowable.task.req.TaskRemotePageReq;
import com.gok.bcp.flowable.task.req.WorkflowRemotePageReq;
import com.gok.bcp.flowable.task.req.WorkflowReq;
import com.gok.bcp.flowable.task.vo.*;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.dto.WorkFlowPageDTO;
import com.gok.pboot.portal.enums.WorkFlowTypeEnum;
import com.gok.pboot.portal.service.IWorkFlowTaskService;
import com.gok.pboot.portal.vo.WorkFlowTaskVo;
import com.gok.pboot.portal.vo.WorkflowTaskTotal;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务服务 IMPL
 *
 * <AUTHOR>
 * @date 27/10/2023
 */
@Service
@RequiredArgsConstructor
public class WorkFlowTaskServiceImpl implements IWorkFlowTaskService {

    private final RemoteTaskService remoteTaskService;

    private final RemoteWorkflowService remoteWorkflowService;

    private final RemoteBcpDictService remoteBcpDictService;

    /**
     * 获取远程任务页面
     *
     * @param workFlowPageDTO {@link WorkFlowPageDTO} 分页参数
     * @return {@link R}<{@link Page}<{@link WorkFlowTaskVo}>>
     */
    @Override
    public R<Page<WorkFlowTaskVo>> getRemotePage(WorkFlowPageDTO workFlowPageDTO) {
        Page<WorkFlowTaskVo> voPage = new Page<>(workFlowPageDTO.getPageNo(), workFlowPageDTO.getPageSize());
        List<WorkFlowTaskVo> voList = new ArrayList<>();
        // 1、判断查询类别
        // 1.1、任务状态 首页查询与 more 查询传递参数不一致
        if (CollUtil.isNotEmpty(workFlowPageDTO.getStatusList()) && workFlowPageDTO.getStatusList().size() < 3) {
            workFlowPageDTO.setWorkflowTaskType(WorkFlowTypeEnum.TASK);
        }
        // 1.2、流程状态
        if (workFlowPageDTO.getFlowStatus() != null) {
            workFlowPageDTO.setWorkflowTaskType(WorkFlowTypeEnum.WORKFLOW);
        }
        // 2、通过前端传来的类别查询不同的接口
        switch (workFlowPageDTO.getWorkflowTaskType()) {
            case WORKFLOW:
                getWorkflowPageVo(voList, workFlowPageDTO);
                break;
            case TASK:
                getTaskPageVo(voList, workFlowPageDTO);
                break;
            default:
                // 3、获取全部数据
                getWorkflowPageVo(voList, workFlowPageDTO);
                getTaskPageVo(voList, workFlowPageDTO);
                break;
        }
        voPage.setTotal(voList.size());
        voPage.setRecords(createTimeDesc(voList, workFlowPageDTO));
        return R.ok(voPage);
    }

    /**
     * 任务详情
     *
     * @param id 编号
     * @return {@link R}<{@link TaskDetailVo}>
     */
    @Override
    public R<TaskDetailVo> getTaskById(Long id) {
        return remoteTaskService.getTaskById(id);
    }

    /**
     * 按 ID 获取任务跳转链接
     *
     * @param id {@link Long} 编号
     * @return {@link R}<{@link String}>
     */
    @Override
    public R<String> getWorkflowById(Long id) {
        return remoteWorkflowService.detailView(SecurityUtils.getUser().getId(), id);
    }

    /**
     * 状态同步
     *
     * @param taskStatusSyncDto {@link TaskStatusSyncDto} 任务状态同步
     * @return {@link R}<{@link Boolean}>
     */
    @Override
    public R<Boolean> statusSync(TaskStatusSyncDto taskStatusSyncDto) {
        return remoteTaskService.statusSync(taskStatusSyncDto);
    }

    /**
     * 创建时间降序排序返回
     *
     * @param voList {@link List}<{@link WorkFlowTaskVo}>
     * @param dto {@link WorkFlowPageDTO}
     * @return {@link List}<{@link WorkFlowTaskVo}>
     */
    private List<WorkFlowTaskVo> createTimeDesc(List<WorkFlowTaskVo> voList, WorkFlowPageDTO dto) {
        return voList.stream()
                .sorted(Comparator.comparing(WorkFlowTaskVo::getCreateTime).reversed())
                .skip((dto.getPageNo() - NumberUtils.INTEGER_ONE) * dto.getPageSize())
                .limit(dto.getPageSize())
                .collect(Collectors.toList());
    }

    /**
     * 获取任务状态总计
     *
     * @return {@link R}<{@link WorkflowTaskTotal >
     */
    @Override
    public R<WorkflowTaskTotal> total(){
        PigxUser user = SecurityUtils.getUser();
        // 1、获取任务状态集合
        List<TaskStatusCountVo> taskStatusCountList = remoteTaskService
                .getTaskStatusTotal(user.getId(), null)
                .getData();
        // 2、获取任务总个数（除已完成）
        Long taskTotal = NumberUtils.LONG_ZERO;
        for (TaskStatusCountVo taskStatusCountVo : taskStatusCountList) {
            if (!taskStatusCountVo.getStatus().equals(TaskStatusEnum.COMPLETED.getValue())) {
                taskTotal += taskStatusCountVo.getCount();
            }
        }
        // 3、获取流程状态
        WorkflowReq workflowReq = new WorkflowReq();
        workflowReq.setUserId(user.getId());
        List<WorkflowStatusCountVo> workFlowCountList = remoteWorkflowService.count(workflowReq).getData();
        // 4、获取流程总个数
        Long workflowTotal = NumberUtils.LONG_ZERO;
        for (WorkflowStatusCountVo vo : workFlowCountList) {
            workflowTotal += vo.getCount();
        }
        return R.ok(new WorkflowTaskTotal(taskStatusCountList, workFlowCountList, taskTotal, workflowTotal));
    }

    /**
     * 获取任务分页信息
     *
     * @param workFlowPageDTO {@link WorkFlowTaskVo}
     * @param voList {@link List}<{@link WorkFlowTaskVo}>
     */
    public void getWorkflowPageVo(List<WorkFlowTaskVo> voList, WorkFlowPageDTO workFlowPageDTO) {
        WorkflowRemotePageReq req = BeanUtil.copyProperties(workFlowPageDTO, WorkflowRemotePageReq.class);
        req.setUserId(SecurityUtils.getUser().getId());
        List<WorkflowPageVo> list = remoteWorkflowService.getRemoteList(req).getData();
        if (CollectionUtils.isNotEmpty(list)) {
            // 1、获取应用来源字典
            Map<String, String> dictMap = remoteBcpDictService.getDictKvList("WORKFLOW_DATA_SOURCE_KEY").getData()
                    .stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));
            // 2、封装分页信息
            list.forEach(w -> {
                WorkFlowTaskVo vo = BeanUtil.copyProperties(w, WorkFlowTaskVo.class);
                vo.setWorkflowTaskType(WorkFlowTypeEnum.WORKFLOW.getValue());
                vo.setWorkflowSource(w.getSource());
                vo.setWorkflowSourceStr(dictMap.get(w.getSource()));
                voList.add(vo);
            });
        }
    }

    /**
     * 获取流程分页信息
     *
     * @param workFlowPageDTO {@link WorkFlowTaskVo}
     * @param voList {@link List}<{@link WorkFlowTaskVo}>
     */
    public void getTaskPageVo(List<WorkFlowTaskVo> voList, WorkFlowPageDTO workFlowPageDTO) {
        List<String> statusList = Optional.ofNullable(workFlowPageDTO.getStatusList()).orElse(new ArrayList<>());
        // 1、防止查询任务状态为空数组
        if (statusList.isEmpty()) {
            statusList.add(TaskStatusEnum.NOT_STARTED.getValue());
            statusList.add(TaskStatusEnum.IN_PROGRESS.getValue());
            statusList.add(TaskStatusEnum.DELAYED.getValue());
        }
        // 2、防止查询【已完成】数据过大造成压力
        statusList.remove(TaskStatusEnum.COMPLETED.getValue());
        // 3、获取查询信息
        TaskRemotePageReq taskRemotePageReq = BeanUtil.copyProperties(workFlowPageDTO, TaskRemotePageReq.class);
        taskRemotePageReq.setUserId(SecurityUtils.getUser().getId());
        List<TaskPageVo> list = remoteTaskService.getRemoteList(taskRemotePageReq).getData();
        if (CollectionUtils.isNotEmpty(list)) {
            // 4、封装返回信息
            list.forEach(w -> {
                WorkFlowTaskVo vo = BeanUtil.copyProperties(w, WorkFlowTaskVo.class);
                vo.setWorkflowTaskType(WorkFlowTypeEnum.TASK.getValue());
                vo.setTaskSource(w.getSource());
                vo.setTaskSourceStr(w.getSourceStr());
                voList.add(vo);
            });
        }
    }
}
