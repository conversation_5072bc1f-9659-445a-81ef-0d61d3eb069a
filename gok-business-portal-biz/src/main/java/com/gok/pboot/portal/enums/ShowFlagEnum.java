package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 展示标记枚举类
 *
 * <AUTHOR>
 * @since 2023-08-09
 **/
@Getter
public enum ShowFlagEnum implements ValueEnum<Integer> {

    /**
     * 可展示
     */
    YES(0, "可展示"),

    /**
     * 不展示
     */
    NO(1, "不展示");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    ShowFlagEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 判断字典id是否合法
     *
     * @param value 字典id
     * @return 合法-true 非法-false 空-false
     */
    public static boolean isValueLegal(Integer value) {
        if (!Optional.ofNullable(value).isPresent()) {
            return false;
        }
        Optional<ShowFlagEnum> optional = Arrays.asList(ShowFlagEnum.values()).stream()
                .filter(e -> value.equals(e.getValue()))
                .findAny();
        return optional.isPresent();
    }

}
