package com.gok.pboot.portal.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 项目反馈分页vo
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeedbackVo {


    /**
     * ID
     */
    private Long id;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型（0:优化建议、1：功能异常、2:其他）
     */
    private Integer type;

    /**
     * 反馈类型Str
     */
    private String typeStr;

    /**
     * 系统
     */
    private String applicationName;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 处理情况（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    private Integer handlingSituation;

    /**
     * 处理情况Str
     */
    private String handlingSituationStr;

    /**
     * 评价
     */
    private Integer evaluated;


}
