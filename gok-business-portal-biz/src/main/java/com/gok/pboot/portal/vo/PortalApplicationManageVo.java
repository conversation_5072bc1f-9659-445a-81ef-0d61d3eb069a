package com.gok.pboot.portal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 应用管理Vo
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PortalApplicationManageVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 应用分类id
     */
    private Long categoryId;

    /**
     * 所属应用id
     */
    private Long belongApplicationId;

    /**
     * 所属应用id
     */
    private String belongApplicationName;
    /**
     * 权重
     */
    private Integer weight;

    /**
     * 上架状态
     * 0已上架
     * 1未上架
     */
    private Integer status;

    /**
     * 上架时间
     */
    private LocalDateTime lunchTime;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 链接地址
     */
    private String linkAddress;

    /**
     * 使用文档地址
     */
    private String documentAddress;
}

