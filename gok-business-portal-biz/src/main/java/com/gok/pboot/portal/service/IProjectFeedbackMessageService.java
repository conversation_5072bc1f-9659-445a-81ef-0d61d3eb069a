package com.gok.pboot.portal.service;

import com.gok.pboot.portal.db.entity.ProjectFeedbackMessage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【project_feedback_message(反馈消息表)】的数据库操作Service
* @createDate 2023-08-24 16:11:53
*/
public interface IProjectFeedbackMessageService extends IService<ProjectFeedbackMessage> {

    /**
     * 新增反馈消息
     * @param type 操作类型
     * @param feedbackId 反馈id
     * @param recipient 接受人
     * @param recipientId 接受人id
     * @param content 消息内容
     * @param zentaoUrl 禅道链接
     */
    void insertMassage(Integer type, Long feedbackId, String recipient, Long recipientId, String content, String zentaoUrl);


    /**
     * 通过反馈 ID 获取
     * 通过反馈 ID 获取
     *
     * @param feedbackId  反馈 ID
     * @param triggerType 触发器类型
     * @return {@link ProjectFeedbackMessage}
     */
    ProjectFeedbackMessage getByFeedbackIdAndType(Long feedbackId,Integer triggerType);
}
