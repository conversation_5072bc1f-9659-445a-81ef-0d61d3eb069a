package com.gok.pboot.portal.vo;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 已处理工单 VO
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
public class WorkOrderProcessedVo {

    /**
     * 指派处理人员Id
     */
    private Long handleWorkerId;

    /**
     * 指派处理人员
     */
    private String handleWorker;

    /**
     * 跟进人Id
     */
    private Long followedWorkerId;

    /**
     * 跟进人
     */
    private String followedWorker;

    /**
     * 紧急程度
     */
    private Integer urgencyDegree;

    /**
     * 紧急程度中文
     */
    private String urgencyDegreeStr;

    /**
     * 反馈人ID
     */
    private Long feedbackUserId;

    /**
     * 反馈人
     */
    private Long createBy;

    /**
     * 部门
     */
    private String dept;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈时间
     */
    private LocalDateTime createTime;

    /**
     * 系统
     */
    private String applicationName;

    /**
     * 相关图片
     */
    private List<String> fileIds;

    /**
     * 批转信息
     */
    private List<ProjectFeedbackLogVo> logVos;


}
