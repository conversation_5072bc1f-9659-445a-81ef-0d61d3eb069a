package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 应用视图Vo
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationViewVo {

    /**
     * 主键id
     */
    private Long categoryId;

    /**
     * 应用分类名称
     */
    private String categoryName;

    /**
     * 所属应用列表
     */
    List<ApplicationIconVo> applicationIconVoList;

}
