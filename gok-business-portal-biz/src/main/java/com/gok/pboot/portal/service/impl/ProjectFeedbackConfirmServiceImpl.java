package com.gok.pboot.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.portal.db.entity.ProjectFeedbackConfirm;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackConfirmMapper;
import com.gok.pboot.portal.service.IProjectFeedbackConfirmService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 反馈确认表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Service
public class ProjectFeedbackConfirmServiceImpl extends ServiceImpl<ProjectFeedbackConfirmMapper, ProjectFeedbackConfirm>
    implements IProjectFeedbackConfirmService {

}




