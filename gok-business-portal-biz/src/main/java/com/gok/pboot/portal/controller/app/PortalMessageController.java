package com.gok.pboot.portal.controller.app;

import com.gok.bcp.message.feign.RemoteMessageService;
import com.gok.bcp.message.res.BasePageRes;
import com.gok.bcp.message.vo.BcpMessageVO;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.WorkFlowPageDTO;
import com.gok.pboot.portal.service.IPortalMessageService;
import com.gok.pboot.portal.vo.MessageVo;
import com.gok.pboot.portal.vo.WorkFlowTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 移动端-消息列表
 *
 * <AUTHOR>
 * @description 移动端-消息列表
 * @menu 移动端-消息列表
 * @date 2024/03/01
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "移动端-消息列表")
@RequestMapping("/portal-message")
public class PortalMessageController {

    private final IPortalMessageService iPortalMessageService;

    private final RemoteMessageService remoteMessageService;

    /**
     * 移动端-消息列表
     *
     * @return {@link R}<{@link List}<{@link MessageVo}>>
     */
    @GetMapping("/message-group")
    @ApiOperation(value = "移动端-消息列表", notes = "移动端-消息列表")
    public R<List<MessageVo>> getMessageGroup() {
        return R.ok(iPortalMessageService.getMessageGroup());
    }

    /**
     * 移动端-单平台消息列表
     *
     * @param source  {@link String} 项目code
     * @param current {@link Long} 当前页
     * @param size    {@link Long} 分页条数
     * @return {@link R}<{@link List}<{@link BcpMessageVO}>>
     */
    @GetMapping("/message/page")
    @ApiOperation(value = "移动端-单平台消息列表", notes = "移动端-单平台消息列表")
    public R<BasePageRes<BcpMessageVO>> getMessageByProject(@RequestParam("source") String source,
                                                            @RequestParam("pageNo") Long current,
                                                            @RequestParam("pageSize") Long size) {
        return R.ok(iPortalMessageService.getMessageByProject(source, current, size));
    }

    /**
     * 根据消息id更新消息状态
     *
     * @param messageId {@link Long} 消息id
     * @param status    {@link String} 状态
     * @return {@link R}<{@link String}>
     */
    @PutMapping("/message")
    @ApiOperation(value = "根据消息id更新消息状态", notes = "根据消息id更新消息状态")
    public R<String> updateMessageStatusById(@RequestParam("messageId") Long messageId,
                                             @RequestParam("status") String status) {
        return remoteMessageService.updateMessageStatusById(messageId, status);
    }

    /**
     * 移动端-代办（任务/流程）列表
     *
     * @return {@link R}<{@link List}<{@link WorkFlowTaskVo}>>
     */
    @GetMapping("/task-group")
    @ApiOperation(value = "移动端-代办（任务/流程）列表", notes = "移动端-代办（任务/流程）列表")
    public R<List<WorkFlowTaskVo>> getTaskGroup() {
        return R.ok(iPortalMessageService.getTaskGroup());
    }

    /**
     * 移动端-单平台代办列表
     *
     * @param workFlowPageDTO {@link WorkFlowPageDTO} 分页参数封装
     * @return {@link R}<{@link BasePageRes}<{@link WorkFlowTaskVo}>>
     */
    @GetMapping("/task")
    @ApiOperation(value = "移动端-单平台代办列表", notes = "移动端-单平台代办列表")
    public R<BasePageRes<WorkFlowTaskVo>> getTaskByPage(WorkFlowPageDTO workFlowPageDTO) {
        return R.ok(iPortalMessageService.getTaskByPage(workFlowPageDTO));
    }
}
