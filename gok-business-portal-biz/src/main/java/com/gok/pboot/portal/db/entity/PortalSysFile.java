package com.gok.pboot.portal.db.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
    * 文件管理表
    */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_file")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "文件管理表")
public class PortalSysFile extends Model<PortalSysFile> {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 桶名
     */
    @ApiModelProperty(value = "桶名")
    private String bucketName;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String original;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String type;

    /**
    * 文件大小
    */
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    /**
    * 上传时间
    */
    @ApiModelProperty(value = "上传时间")
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * delFlag
     */
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
    * 所属租户
    */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

    /**
    * 上传状态
    */
    @ApiModelProperty(value = "上传状态")
    private Boolean fileState;

    /**
    * 访问路径
    */
    @ApiModelProperty(value = "访问路径")
    private String fileUrl;
}