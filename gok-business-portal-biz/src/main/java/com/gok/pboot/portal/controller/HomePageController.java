package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.service.IHomePageService;
import com.gok.pboot.portal.vo.ContentInfoVo;
import com.gok.pboot.portal.vo.RecommendedClassVo;
import com.gok.pboot.portal.vo.TrainingPlanVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 门户首页表 前端控制器
 * <AUTHOR>
 * @description 门户首页表 前端控制器
 * @menu 门户首页表
 * @since 2023-08-01
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/homePage")
@Api(tags = "门户首页表")
public class HomePageController {

    private final IHomePageService homePageService;

    @GetMapping("userInfo")
    @ApiOperation(value = "查询门户首页-个人信息", notes = "查询门户首页-个人信息")
    public R getUserInfo(){
        return R.ok(homePageService.getUserInfo());
    }

    /**
     * 获取培训计划
     *
     * @param page 页
     * @return {@link R}<{@link Page}<{@link TrainingPlanVo}>>
     */
    @GetMapping("trainingPlan")
    @ApiOperation(value = "查询门户首页-培训文件", notes = "查询门户首页-培训文件")
    public R<Page<TrainingPlanVo>> getTrainingPlan(Page page){
        return R.ok(homePageService.getTrainingPlan(page));
    }

    /**
     * 获取内容
     *
     * @param contentId 内容 ID
     * @return {@link R}<{@link ContentInfoVo}>
     */
    @GetMapping("content/{contentId}")
    @ApiOperation(value = "查询门户首页-内容查看", notes = "查询门户首页-内容查看")
    public R<ContentInfoVo> getContent(@PathVariable(value = "contentId") Long contentId){
        return R.ok(homePageService.getContent(contentId));
    }

    /**
     * 获取推荐课程
     *
     * @param phone 电话
     * @return {@link R}<{@link List}<{@link RecommendedClassVo}>>
     */
    @GetMapping("recommendedClass/{phone}")
    @ApiOperation(value = "查询门户首页-推荐课程", notes = "查询门户首页-推荐课程")
    public R<List<RecommendedClassVo>> getRecommendedClass(@PathVariable(value = "phone") String phone){
        return R.ok(homePageService.getRecommendedClass(phone));
    }

}
