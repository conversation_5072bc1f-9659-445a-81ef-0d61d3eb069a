package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务流程枚举
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@Getter
@AllArgsConstructor
public enum WorkFlowTypeEnum implements ValueEnum<String> {

    /**
     * 全部
     */
    ALL("ALL", "全部"),

    /**
     * 流程
     */
    WORKFLOW("WORKFLOW", "流程"),

    /**
     * 任务
     */
    TASK("TASK", "任务");

    private final String value;

    private final String name;
}
