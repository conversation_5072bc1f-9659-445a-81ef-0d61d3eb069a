package com.gok.pboot.portal.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.portal.enums.UrlEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 拼接路径
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Slf4j
@Component
public class PackageUrlUtils {

    private final String OA_FAILED_RESPONSE = "Token获取失败";

    /**
     * 应用id与应用标识
     */
    @Value("#{${single-login.appMap}}")
    private Map<String, String> appMap;

    /**
     * 应用标识与密钥key
     */
    @Value("#{${single-login.keyMap}}")
    private Map<String, String> keyMap;

    /**
     * OA系统获取Token地址
     */
    @Value("${oa.url.getToken}")
    private String getTokenUrl;

    /**
     * OA系统的应用id
     */
    @Value("${oa.appId}")
    private String appId;

    /**
     * 产融租户id
     */
    @Value("${single-login.tenantId}")
    private String tenantId;

    /**
     * 返回单点登录后的路径
     *
     * @param url 原路径
     * @param applicationId 应用id
     *  @param loginId OA用户id
     * @return 新路径
     */
    public String getUrl(String url, Long applicationId, String loginId) {
        // 当前登录用户信息
        PigxUser user = UserUtils.getUser();
        // 产融教学、租户跳转地址拼接
        final String eduOrtenUrl = "/login-center?time=%s&token=%s&account=%s&tenantId=%s";
        // 当前秒级时间戳
        final long time = System.currentTimeMillis() / 1000;
        // 用户手机号
        String account = user.getPhone();
        // 通过应用id获取其应用标识
        String appCode = appMap.get(applicationId.toString());
        // 通过应用标识获取其应用密钥key
        String key = keyMap.get(appCode);
        String newUrl = "";
        // 3.1、校验比对域名
        if (UrlEnum.IOIE_EDU.getValue().equals(appCode)) {
            newUrl = url + String.format(eduOrtenUrl, time, DigestUtils.md5Hex(UrlEnum.IOIE_EDU.getValue() + key + time), account, tenantId);
            log.info("IOIE_EDU:{}", newUrl);
            return newUrl;
        } else if (UrlEnum.IOIE_TENANT.getValue().equals(appCode)) {
            newUrl = url + String.format(eduOrtenUrl, time, DigestUtils.md5Hex(UrlEnum.IOIE_TENANT.getValue() + key + time), account, tenantId);
            log.info("IOIE_TENANT:{}", newUrl);
            return newUrl;
        } else if (UrlEnum.IOIE_OPERATION.getValue().equals(appCode)) {
            final String operationUrl = "/login-center?time=%s&token=%s&account=%s";
            newUrl = url + String.format(operationUrl, time, DigestUtils.md5Hex(UrlEnum.IOIE_OPERATION.getValue() + key + time), account);
            log.info("IOIE_OPERATION:{}", newUrl);
            return newUrl;
        } else if (UrlEnum.ZENTAO.getValue().equals(appCode)) {
            final String zenTaoUrl = "/api.php?m=user&f=apilogin&account=%s&code=%s&time=%s&token=%s";
            newUrl = url + String.format(zenTaoUrl, loginId, appCode, time, DigestUtils.md5Hex(UrlEnum.ZENTAO.getValue() + key + time));
            log.info("ZENTAO:{}", newUrl);
            return newUrl;
        } else if (UrlEnum.OA.getValue().equals(appCode)) {
            newUrl = url + "/wui/index.html?ssoToken=" + getRedirectOaToken(getTokenUrl , appId, loginId);
            log.info("OA:{}", newUrl);
            return newUrl;
        }

        log.info("未匹配到对应的应用id返回其完整URL");
        return StrUtil.EMPTY;
    }

    /**
     * 获取OA的token
     *
     * @param oaUrl 原路径
     * @param appId 应用id
     * @param loginId OA用户id
     * @return token
     */
    private String getRedirectOaToken(String oaUrl, String appId, String loginId) {
        HttpRequest httpRequest = HttpUtil.createPost(oaUrl);
        // 设置头字段
        httpRequest.header("application/x-www-form-urlencoded");
        // 设置内容信息
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("appid", appId);
        paramMap.put("loginid", loginId);
        httpRequest.form(paramMap);
        // 处理返回结果
        String resCont = httpRequest.execute().body();
        log.info("请求OA Token接口响应结果为：{}", resCont);
        if (StrUtil.contains(resCont, OA_FAILED_RESPONSE)) {
            log.info("接口请求失败，获取跳转token失败");
            return StrUtil.EMPTY;
        }

        return resCont;
    }
}
