package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.common.core.validation.annotation.LongVerify;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 门户背景 Vo类
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortalBackgroundVo {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 适用日期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    /**
     * 适用日期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    /**
     * 个人生日适用类型（0不适用，1适用）
     * {@link com.gok.pboot.portal.enums.SuitTypeEnum}
     */
    private Integer birthday;

    /**
     * 入职周年适用类型（0不适用，1适用）
     * {@link com.gok.pboot.portal.enums.SuitTypeEnum}
     */
    private Integer anniversary;

    /**
     * 上架状态 字典id
     * {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     */
    private Integer status;

    /**
     * 上架状态 字典值
     */
    private String statusTxt;

    /**
     * 门户背景文件id
     */
    private Long portalFileId;

    /**
     * 门户背景文件访问路径
     */
    private String portalFileUrl;

    /**
     * 个人背景文件id
     */
    private Long personFileId;

    /**
     * 个人背景文件访问路径
     */
    private String personFileUrl;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 默认标志位
     * {@link com.gok.pboot.portal.enums.BackgroundTypeEnum}
     */
    private Integer defaultFlag;

    /**
     * 创建者名称
     */
    @ApiModelProperty(value = "创建者名称")
    private String createUserName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 个人背景文件id
     */
    @LongVerify(name = "门户背景文件id")
    private Long appFileId;

    /**
     * 个人背景文件文件访问路径
     */
    private String appFileUrl;

}
