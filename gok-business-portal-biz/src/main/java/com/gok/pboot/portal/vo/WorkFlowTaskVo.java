package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.flowable.task.common.enums.task.PriorityEnum;
import com.gok.bcp.flowable.task.common.enums.task.TaskStatusEnum;
import com.gok.bcp.flowable.task.common.enums.task.TaskTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作流程视图
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@Data
public class WorkFlowTaskVo implements Serializable {

    /**
     * 任务/流程类型
     */
    private String workflowTaskType;

    // 任务/消息信息

    /**
     * 任务id
     */
    private Long id;

    /**
     * 任务来源 {@link SourceEnum}
     */
    private String taskSource;

    /**
     * 任务来源文本 {@link SourceEnum}
     */
    private String taskSourceStr;

    /**
     * 任务类型 {@link TaskTypeEnum}
     */
    private String type;

    /**
     * 任务类型文本
     */
    private String typeStr;

    /**
     * 作业类型
     */
    private String jobType;

    /**
     * 作业类型文本
     */
    private String jobTypeStr;

    /**
     * 所属项目id
     */
    private String relateId;

    /**
     * 跳转链接
     */
    private String jumpLink;

    /**
     * 所属项目
     */
    private String relateName;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 优先级文本 {@link PriorityEnum}
     */
    private String priorityStr;

    /**
     * 优先级 {@link PriorityEnum}
     */
    private Integer priority;

    /**
     * 负责人id
     */
    private String leaderId;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 状态 {@link TaskStatusEnum}
     */
    private String status;

    /**
     * 状态文本
     */
    private String statusStr;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    /**
     * 截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime receiveTime;

    // 流程信息

    /**
     * 来源
     */
    private String workflowSource;

    /**
     * 关联id
     */
    private String otherId;

    /**
     * 来源文本
     */
    private String workflowSourceStr;

    /**
     * 流程类型
     */
    private String workflowType;

    /**
     * 流程标题
     */
    private String title;

    /**
     * 所属路径
     */
    private String path;

    /**
     * 当前节点
     */
    private String currentNode;

    /**
     * 未操作者
     */
    private String unOperators;

    /**
     * 创建人
     */
    private String creator;

    // 移动端

    /**
     * 未读数量
     */
    private Long unStartedCount;
}
