package com.gok.pboot.portal.db.mapper;

import com.gok.components.data.datascope.BaseMapper;

import com.gok.pboot.portal.db.entity.SysDictItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典项表 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
public interface SysDictItemMapper  extends BaseMapper<SysDictItem> {

    /**
     * 根据字典类型查询字典项
     * @param dictType
     * @return
     */
    List<SysDictItem> selByType(@Param("dictType") String dictType);
}