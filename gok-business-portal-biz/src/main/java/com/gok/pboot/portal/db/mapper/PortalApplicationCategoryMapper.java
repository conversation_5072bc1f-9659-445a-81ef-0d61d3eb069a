package com.gok.pboot.portal.db.mapper;

import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.PortalApplicationCategory;
import com.gok.pboot.portal.vo.PortalApplicationCategoryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 应用分类 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Mapper
public interface PortalApplicationCategoryMapper extends BaseMapper<PortalApplicationCategory> {

    /**
     * 全部逻辑删除
     * @param name 修改人姓名
     */
    void delFlagAll(@Param("userName") String name);

    /**
     * 获取应用分类以及该分类下所属的应用个数
     * @return
     */
    List<PortalApplicationCategoryVo> queryCategoryWithCount();
}
