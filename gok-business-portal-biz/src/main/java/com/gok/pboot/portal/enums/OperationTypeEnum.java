package com.gok.pboot.portal.enums;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 操作类型枚举类
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Getter
public enum OperationTypeEnum implements ValueEnum<Integer> {

    /**
     * 创建
     */
    CREATE(0, "创建"),

    /**
     * 编辑
     */
    EDIT(1, "编辑"),

    /**
     * 删除
     */
    DELETE(2, "删除"),

    /**
     * 上架
     */
    UP(3, "上架"),

    /**
     * 下架
     */
    DOWN(4, "下架");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    OperationTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 判断字典id是否合法
     *
     * @param value 字典id
     * @return 合法-true 非法-false 空-false
     */
    public static boolean isValueLegal(Integer value) {
        if (!Optional.ofNullable(value).isPresent()) {
            return false;
        }
        Optional<OperationTypeEnum> optional = Arrays.asList(OperationTypeEnum.values()).stream()
                .filter(e -> value.equals(e.getValue()))
                .findAny();
        return optional.isPresent();
    }

    public static String getNameByValue(Integer value) {
        OperationTypeEnum operationTypeEnum = EnumUtil.getBy(OperationTypeEnum.class, e -> e.getValue().equals(value));
        if (ObjectUtil.isNotNull(operationTypeEnum)) {
            return operationTypeEnum.getName();
        } else {
            return null;
        }
    }

}
