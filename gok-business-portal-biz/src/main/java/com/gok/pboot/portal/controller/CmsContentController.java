package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.portal.dto.CmsContentDto;
import com.gok.pboot.portal.dto.CmsContentSaveOrUpdateDto;
import com.gok.pboot.portal.service.ICmsContentService;
import com.gok.pboot.portal.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
/**
 * 内容管理表 前端控制器
 *
 * <AUTHOR>
 * @description 内容管理表 前端控制器
 * @menu 内容管理
 * @since 2023-06-08
 **/

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/cmsContent")
@Api(tags = "内容管理表")
public class CmsContentController {

    private final ICmsContentService service;

    /**
     * 分页查询内容列表
     *
     * @param page    分页请求
     * @param request 查询参数
     * @return {@link R}<{@link Page}<{@link CmsContentVo}>>
     */
    @GetMapping("/findPage")
    @ApiOperation(value = "分页查询内容列表", notes = "分页查询内容列表")
    public R<Page<CmsContentVo>> findPage(Page page, CmsContentDto request) {
        return R.ok(service.findPage(page, request));
    }

    /**
     * 查询内容详情
     *
     * @param contentId 内容id
     * @param isAdmin 是否为管理员
     * @return {@link R}<{@link CmsContentDetailVo}>
     */
    @GetMapping("/{contentId}")
    @ApiOperation(value = "查询内容详情", notes = "查询内容详情")
    public R<CmsContentDetailVo> findDetail(@PathVariable Long contentId,@RequestParam(required = false) boolean isAdmin) {
        return R.ok(service.findDetail(contentId,isAdmin));
    }

    /**
     * 新增内容
     *
     * @param request 新增请求
     * @return {@link R}
     */
    @PostMapping
    @ApiOperation(value = "新增内容", notes = "新增内容")
    public R save(@Valid @RequestBody CmsContentSaveOrUpdateDto request) {
        if (request.getPublishTime() != null && LocalDateTime.now().isAfter(request.getPublishTime())) {
            return R.failed("发布时间不能小于当前时间");
        }
        return R.ok(service.save(request));
    }

    /**
     * 编辑内容
     *
     * @param request 编辑请求
     * @return {@link R}
     */
    @PutMapping
    @ApiOperation(value = "编辑内容", notes = "编辑内容")
    public R update(@Valid @RequestBody CmsContentSaveOrUpdateDto request) {
        return R.ok(service.update(request));
    }

    /**
     * 上下架内容
     *
     * @param id     内容主键id
     * @param status 上下架状态字典id {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     * @return {@link R}
     */
    @PutMapping("/status/{id}")
    @ApiOperation(value = "上下架内容", notes = "上下架内容")
    public R updateStatus(@PathVariable("id") Long id, @RequestParam Integer status) {
        return R.ok(service.updateStatus(id, status));
    }

    /**
     * 批量删除内容
     *
     * @param ids 内容主键id集合[]
     * @return {@link R}
     */
    @DeleteMapping
    @ApiOperation(value = "批量删除内容", notes = "批量删除内容")
    public R batchDelete(@RequestBody List<Long> ids) {
        return R.ok(service.batchDelete(ids));
    }

    /**
     * 按发布时间发送消息
     *
     * @return {@link List}<{@link CmsContentPublishVo}>
     */
    @Inner
    @PostMapping("sendMsgByPublishTime")
    @ApiOperation(value = "按发布时间发送消息", notes = "按发布时间发送消息")
    public List<CmsContentPublishVo> sendMsgByPublishTime(){
        return service.sendMsgByPublishTime();
    }


    /**
     * 查找公文分类计数
     *
     *
     * @return {@link R}<{@link CmsContentDetailVo}>
     */
    @GetMapping("/doc/count")
    @ApiOperation(value = "查找公文分类计数", notes = "查找公文分类计数")
    public R<CmsDocCountVo> findSortCount() {
        return R.ok(service.findSortCount());
    }
}
