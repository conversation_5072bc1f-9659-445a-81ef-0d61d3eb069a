package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 背景类型的枚举类
 *
 * <AUTHOR>
 * @date 2023/08/09
 */
@Getter
public enum BackgroundTypeEnum implements ValueEnum<Integer> {

    /**
     * 非默认背景
     */
    NOT_DEFAULT(0, "非默认背景"),

    /**
     * 默认背景
     */
    DEFAULT(1, "默认背景");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    BackgroundTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
