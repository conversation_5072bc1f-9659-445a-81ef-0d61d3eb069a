package com.gok.pboot.portal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 应用管理请求Dto(新增和修改）
 * <AUTHOR>
 */

@Data
public class PortalApplicationManageSaveOrUpdateDto {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 应用分类id
     */
    private Long categoryId;

    /**
     * 所属应用id
     */
    private Long belongApplicationId;

    /**
     * 所属应用名称
     */
    private String belongApplicationName;

    /**
     * 应用跳转链接
     */
    private String linkAddress;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 上架状态
     * 0已上架
     * 1未上架
     */
    private Integer status;

    /**
     * 文件id
     */
    private Long fileId;
}
