package com.gok.pboot.portal.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.portal.db.entity.ProjectFeedbackMessage;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackMessageMapper;
import com.gok.pboot.portal.service.IProjectFeedbackMessageService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 反馈消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Service
public class ProjectFeedbackMessageServiceImpl extends ServiceImpl<ProjectFeedbackMessageMapper, ProjectFeedbackMessage>
    implements IProjectFeedbackMessageService {

    @Autowired
    private ProjectFeedbackMessageMapper projectFeedbackMessageMapper;
    @Override
    public void insertMassage(Integer type, Long feedbackId, String recipient, Long recipientId, String content, String zentaoUrl) {
        ProjectFeedbackMessage projectFeedbackMessage=new ProjectFeedbackMessage();
        projectFeedbackMessage.setTriggerType(type);
        projectFeedbackMessage.setFeedbackId(feedbackId);
        projectFeedbackMessage.setRecipient(recipient);
        projectFeedbackMessage.setRecipientId(recipientId);
        projectFeedbackMessage.setMessageContent(content);
        if (zentaoUrl!=null){
            projectFeedbackMessage.setZentaoUrl(zentaoUrl);
        }else {
            projectFeedbackMessage.setZentaoUrl(StrUtil.SPACE);
        }

        projectFeedbackMessageMapper.insert(projectFeedbackMessage);
    }

    @Override
    public ProjectFeedbackMessage getByFeedbackIdAndType(Long feedbackId,Integer triggerType) {
        ProjectFeedbackMessage projectFeedbackMessage = baseMapper.getByFeedbackIdAndType(feedbackId,triggerType);
        return projectFeedbackMessage;
    }
}




