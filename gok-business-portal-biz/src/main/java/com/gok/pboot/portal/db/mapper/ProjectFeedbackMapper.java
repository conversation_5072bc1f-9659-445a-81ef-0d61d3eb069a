package com.gok.pboot.portal.db.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.portal.db.entity.ProjectFeedback;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.dto.ProjectFeedbackSatisfactionDto;
import com.gok.pboot.portal.dto.WorkOrderPageDto;
import com.gok.pboot.portal.vo.WorkOrderPageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * <P>
 * 针对表【project_feedback(项目反馈表)】的数据库操作Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */

@Mapper
public interface ProjectFeedbackMapper extends BaseMapper<ProjectFeedback> {
    /**
     * 设置满意度
     *
     * @param projectFeedbackSatisfactionDto 项目反馈满意度dto
     */
    void setSatisfaction(@Param("satisfactionDto") ProjectFeedbackSatisfactionDto projectFeedbackSatisfactionDto);

    /**
     * 查询工单管理list
     *
     * @param page          页面
     * @param workOrderPage 工作订单页面
     * @return {@link List}<{@link WorkOrderPageVo}>
     */
    IPage<WorkOrderPageVo> queryWOList(@Param("page") Page page, @Param("dto") WorkOrderPageDto workOrderPage);


    /**
     * 查询处理中的反馈任务
     *
     * @return
     */
    List<ProjectFeedback> selNotClosed();

    /**
     * 修改反馈的状态
     *
     * @param feedbackId 反馈id
     */
    void updateStatusByFeedbackId(@Param("feedbackId") Long feedbackId);
}




