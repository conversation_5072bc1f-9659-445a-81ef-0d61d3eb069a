package com.gok.pboot.portal.dto;


import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Builder;
import lombok.Data;



/**
 * 内容管理编辑封面图url Dto类
 *
 * <AUTHOR>
 * @since 2023-08-09
 **/
@Data
@Builder
public class CmsContentEditCoverUrlDto {

    /**
     * id
     */
    private Long id;

    /**
     * 封面图url
     */
    @StringVerify(name = "封面图url", maxLen = 100)
    private String coverImageUrl;

}
