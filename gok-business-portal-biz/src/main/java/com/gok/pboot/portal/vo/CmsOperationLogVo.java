package com.gok.pboot.portal.vo;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.portal.db.entity.CmsOperationLog;
import com.gok.pboot.portal.enums.OperationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.StringJoiner;

/**
 * 操作记录 Vo类
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CmsOperationLogVo {

    /**
     * id
     */
    private Long id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 操作类型id
     */
    private Integer operationType;

    /**
     * 操作类型字典值
     * {@link com.gok.pboot.portal.enums.OperationTypeEnum}
     */
    private String operationTypeTxt;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 构建 VO
     *
     * @param cmsOperationLog CMS操作日志
     * @return {@link CmsOperationLogVo}
     */
    public static CmsOperationLogVo buildVo(CmsOperationLog cmsOperationLog) {
        CmsOperationLogVo cmsOperationLogVo = BeanUtil.copyProperties(cmsOperationLog, CmsOperationLogVo.class);
        cmsOperationLogVo.setOperationTypeTxt(OperationTypeEnum.getNameByValue(cmsOperationLog.getOperationType()));
        return cmsOperationLogVo;
    }

}
