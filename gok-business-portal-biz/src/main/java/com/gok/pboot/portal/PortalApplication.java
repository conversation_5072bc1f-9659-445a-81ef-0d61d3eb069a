package com.gok.pboot.portal;
import com.gok.pboot.common.feign.annotation.EnablePigxFeignClients;
import com.gok.pboot.common.security.annotation.EnablePigxResourceServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @date 2023/07/31 数字门户服务 
 */
@EnableDiscoveryClient
@EnablePigxResourceServer
@SpringBootApplication
@MapperScan({"com.gok.module.file.mapper","com.gok.pboot.portal.db.mapper"})
@EnablePigxFeignClients(basePackages = {"com.gok"})

public class PortalApplication {
    public static void main(String[] args) {
        SpringApplication.run(PortalApplication.class, args);
        System.out.println("数字门户服务启动成功！！！");
    }
}