package com.gok.pboot.portal.enums;


import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 反馈操作类型枚举类
 *
 * <AUTHOR>
 * @since 2023-08-24
 */

@Getter
public enum FeedbackOperationTypeEnum implements ValueEnum<Integer> {

    //0重新激活,1确定,2取消,3搁置,4已处理,5已解决,6已关闭,7回退
    /**
     * 0重新激活
     */
    REACTIVATION(0, "重新激活"),

    /**
     *  1确定
     */
    CONFIRM(1, "确认"),
    /**
     * 2 取消
     */
    CANCEL(2, "取消"),

    /**
     * 3搁置
     */
    SHELVE(3, "搁置"),

    /**
     * 4已处理
     */
    PROCESSED(4, "已处理"),

    /**
     * 5已解决
     */
    RESOLVED(5, "已解决"),

    /**
     * 6已关闭
     */
    CLOSED(6, "已关闭"),

    /**
     * 7 回退
     */
    ROLLBACK(7,"回退");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FeedbackOperationTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按值获取名称
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String getNameByValue(Integer value){
        if (value == null){
            return null;
        }
        for (FeedbackOperationTypeEnum feedbackOperationTypeEnum : FeedbackOperationTypeEnum.values()) {
            if (feedbackOperationTypeEnum.getValue().equals(value)) {
                return feedbackOperationTypeEnum.getName();
            }
        }
        return null;
    }
}
