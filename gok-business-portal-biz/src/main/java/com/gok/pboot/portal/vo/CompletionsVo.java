package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 完成 vo
 *
 * <AUTHOR>
 * @date 2024/01/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompletionsVo {

    /**
     * 聊天id
     */
    private String id;
    private List<Choices> choices;

    /**
     * 选择
     *
     * <AUTHOR>
     * @date 2024/01/22
     */
    @Data
    public class Choices {
        private Message message;
    }

    /**
     * 消息
     *
     * <AUTHOR>
     * @date 2024/01/22
     */
    @Data
    public class Message {
        private String role;
        private String content;
    }
}
