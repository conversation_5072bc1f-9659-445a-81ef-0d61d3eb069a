package com.gok.pboot.portal.service;

import com.gok.pboot.portal.dto.VodConfirmDto;
import com.gok.pboot.portal.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 视频点播服务
 *
 * <AUTHOR>
 * @Description :  文件上传
 * @menu HuaWeiYun视频服务
 * @date 2024/01/22
 */
public interface VodService {

    /**
     * 获取上传地址和凭证
     *
     * @param fileName 文件名
     * @return {@link VodVo}
     */
    VodVo getAuth(String fileName);

    /**
     * 获取播放地址
     *
     * @param videoKey 视频键
     * @return {@link VodAddressVo}
     */
    VodAddressVo getAddress(String videoKey);

    /**
     * 获取转码进度
     *
     * @param keys 钥匙
     * @return {@link List}<{@link VodTransProgressVo}>
     */
    List<VodTransProgressVo> getTransProgress(List<String> keys);

    /**
     * 获取转码未完成的视频
     * @return
     */
    List<VodTransProgressVo> getTransNotCompleted();

    /**
     * 获取配置信息
     * @return
     */
    HwYunVodConfigVo getConfigInfo();

    /**
     * 确认媒资
     *
     * @param vodConfirmDto VOD 确认 DTO
     * @return {@link VodConfirmVo}
     */
    VodConfirmVo confirmVideo(VodConfirmDto vodConfirmDto);

    /**
     * 获取转码未完成的视频
     *
     * @param map 地图
     * @return {@link List}<{@link VodTransProgressVo}>
     */
    List<VodTransProgressVo> getTransNotCompletedV2(Map<String, Object> map);
}
