package com.gok.pboot.portal.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.ChatMessageDto;
import com.gok.pboot.portal.dto.ListConversationsDto;
import com.gok.pboot.portal.dto.ListMessagesDto;
import com.gok.pboot.portal.exception.ChatException;
import com.gok.pboot.portal.vo.CompletionsVo;
import com.gok.pboot.portal.vo.HistoryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 客服助手机器人（两种，目前启用dify）  1、dify  2、fastGpt
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Slf4j
@Component
public class ChatMessagesUtils {


    /**
     * dify前缀路径
     */
    @Value("${dify.url}")
    private String s1;

    /**
     * dify客服小助手鉴权 开场白
     */
    @Value("${dify.authorizationPrologue}")
    private String authorizationPrologue;

    /**
     * dify客服小助手鉴权 开场白
     */
    @Value("${dify.authorization}")
    private String authorization;

    /**
     * fastGpt客服小助手前缀路径
     */
    @Value("${fastGpt.url}")
    private String fastGptUrl;

    /**
     * fastGpt鉴权key
     */
    @Value("${fastGpt.authorizationKey}")
    private String authorizationKey;

    /**
     * fastGpt鉴权code
     */
    @Value("${fastGpt.authorizationCode}")
    private String authorizationCode;

    /**
     * 超时时间
     */
    public static final Integer TIMEOUT = 60 * 1000;

    /**
     * application/json
     */
    public static final String APP_JSON = "application/json";

    /**
     * BEARER
     */
    public static final String BEARER = "Bearer ";

    /**
     * 当前使用人数较多,请稍后再试
     */
    public static final String TOO_MUCH = "当前使用人数较多,请稍后再试";

    /**
     * dify接口有误
     */
    public static final String ERR_DIFY = "dify接口有误,获取到的内容:{}";


    /**
     * LIMIT
     */
    public static final String LIMIT = "limit";

    /**
     * STATUS
     */
    public static final String STATUS = "status";

    /**
     * CONVERSATION_ID
     */
    public static final String CONVERSATION_ID = "conversation_id";



    /**
     * fastGpt 获取token
     *
     * @param username 用户名
     * @param password 密码
     * @return token
     */
    public String getToken(String username, String password) {
        String getToken = "http://fastgpt.prod.goktech.cn/api/user/account/loginByPassword";
        Map<String, Object> requestMap = new HashMap<>(2);

        requestMap.put("username", username);
        requestMap.put("password", password);
        log.info("登录Map: {}", requestMap);
        String response = HttpRequest.of(getToken, StandardCharsets.UTF_8)
                .method(Method.POST)
                .header(Header.CONTENT_TYPE, APP_JSON)
                .body(JSONObject.toJSONString(requestMap))
                .timeout(TIMEOUT)
                .execute().body();
        log.info("登录response: {}", response);

        Map<String, Object> responseMap = (Map<String, Object>) JSONObject.parse(response);
        if (!Optional.ofNullable(responseMap).isPresent() || !Optional.ofNullable(responseMap.get("data")).isPresent()) {
            log.info("获取token有误");
            return StrUtil.EMPTY;
        }
        Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
        return (String) dataMap.get("token");
    }

    /**
     * fastGpt  获取聊天列表
     *
     * @return {@link Map}
     */
    public Map<String, Object> fastFirstTip() {
        String s1 = "v1/chat/getWelcomeText";

        Map<String, Object> welcomeMap = new HashMap<>(1);
        welcomeMap.put("appId", authorizationCode);

        String response = HttpRequest.of(fastGptUrl + s1, StandardCharsets.UTF_8)
                .method(Method.GET)
                .header(Header.AUTHORIZATION, BEARER + authorizationKey + "-" + authorizationCode)
                .header(Header.CONTENT_TYPE, APP_JSON)
                .body(JSONObject.toJSONString(welcomeMap))
                .timeout(TIMEOUT)
                .execute().body();
        log.info("首次提示语response: {}", response);

        return (Map<String, Object>) JSONObject.parse(response);
    }

    /**
     * fastGpt 聊天
     *
     * @param chatId 聊天id
     * @param content 内容
     * @return 聊天记录
     */
    public CompletionsVo completions(String chatId, String content) {
        String s1 = "v1/chat/completions";
        Map<String, Object> requestMap = new HashMap<>(5);

        Map<String, Object> variablesMap = new HashMap<>(1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        variablesMap.put("cTime", sdf.format(System.currentTimeMillis()));

        List<Map<String, String>> messageList = new ArrayList<>();
        Map<String, String> messagesMap = new HashMap<>(2);
        messagesMap.put("content", content);
        messagesMap.put("role", "user");
        messageList.add(messagesMap);

        requestMap.put("chatId", chatId);
        requestMap.put("stream", false);
        requestMap.put("detail", false);
        requestMap.put("variables", variablesMap);
        requestMap.put("messages", messageList);
        log.info("聊天Map: {}", requestMap);

        String response = HttpRequest.of(fastGptUrl + s1, StandardCharsets.UTF_8)
                .method(Method.POST)
                .header(Header.AUTHORIZATION, BEARER + authorizationKey + "-" + authorizationCode)
                .header(Header.CONTENT_TYPE, APP_JSON)
                .body(JSONObject.toJSONString(requestMap))
                .timeout(TIMEOUT)
                .execute().body();
        log.info("聊天response: {}", response);

        Map<String, Object> responseMap = (Map<String, Object>) JSONObject.parse(response);

        if ("500".equals(responseMap.get("code")) || Integer.valueOf("500").equals(responseMap.get("code"))) {
            throw new ChatException(TOO_MUCH);
        }

        return CompletionsVo.builder()
                .id((String) responseMap.get("id"))
                .choices((List<CompletionsVo.Choices>) responseMap.get("choices"))
                .build();
    }

    /**
     * fastGpt  获取聊天列表
     *
     * @param chatId 聊天id
     * @param limit 聊天条数
     * @return {@link List}<{@link HistoryVo}>
     */
    public List<HistoryVo> getHistoryList(String chatId, Long limit) {
        String s1 = "v1/chat/getHistory";

        Map<String, Object> historyMap = new HashMap<>(3);
        historyMap.put("chatId", chatId);
        historyMap.put("appId", authorizationCode);
        historyMap.put(LIMIT, limit);

        String response = HttpRequest.of(fastGptUrl + s1, StandardCharsets.UTF_8)
                .method(Method.GET)
                .header(Header.AUTHORIZATION, BEARER + authorizationKey + "-" + authorizationCode)
                .header(Header.CONTENT_TYPE, APP_JSON)
                .body(JSONObject.toJSONString(historyMap))
                .timeout(TIMEOUT)
                .execute().body();
        log.info("聊天response: {}", response);

        Map<String, Object> map = (Map<String, Object>) JSONObject.parse(response);
        if (!Optional.ofNullable(map).isPresent() || !Optional.ofNullable(map.get("data")).isPresent()) {
            return new ArrayList<>();
        }
        Map<String, Object> dataMap = (Map<String, Object>) map.get("data");
        List<HistoryVo> historyVoList = (List<HistoryVo>) dataMap.get("history");
        if (CollUtil.isEmpty(historyVoList)) {
            return new ArrayList<>();
        }

        return historyVoList;

    }

    /**
     * dify 首次提示语
     */
    public Map<String, Object> firstTip() {
        String s2 = "v1/parameters";

        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.GET)
                .header(Header.AUTHORIZATION, authorizationPrologue)
                .timeout(TIMEOUT)
                .execute().body();

        return JSONObject.parseObject(response);
    }

    /**
     * dify 创建会话信息或基于此前的对话继续发送消息
     *
     * @param query 询问内容
     * @param conversationId 用户对应的聊天id
     * @param userId 用户id
     * @return 聊天记录
     */
    public R<Object> chatMessage(String query, String conversationId, String chatUserId) {
        String s2 = "v1/chat-messages";

        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("inputs", new ArrayList<>());
        messageMap.put("query", query);
        messageMap.put("response_mode", "blocking");
        messageMap.put(CONVERSATION_ID, conversationId);
        messageMap.put("user", chatUserId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info(chatUserId+"提问："+query);
        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.POST)
                .header(Header.AUTHORIZATION, authorization)
                .header(Header.CONTENT_TYPE, APP_JSON)
                .body(JSONObject.toJSONString(messageMap))
                .timeout(TIMEOUT)
                .execute().body();

        JSONObject jsonObject = JSONObject.parseObject(response);
        Map<String, Object> map = (Map<String, Object>) jsonObject;
        if (Boolean.TRUE.equals(checkCode(map.get(STATUS)))) {
            log.error(ERR_DIFY, map.toString());
            throw new ChatException(TOO_MUCH);
        }

        ChatMessageDto chatMessageDTO = JSONObject.parseObject(JSON.toJSONString(map), ChatMessageDto.class);
        chatMessageDTO.setCreatedAt(String.valueOf(map.get("created_at")));
        chatMessageDTO.setConversationId((String) map.get(CONVERSATION_ID));
        stopWatch.stop();
        log.info(chatUserId+"回答("+stopWatch.getTotalTimeSeconds()+"s)："+chatMessageDTO.getAnswer());
        return R.ok(chatMessageDTO);
    }

    /**
     * dify 获取聊天列表
     *
     * @param conversationId 用户对应的聊天id
     * @param userId 用户id
     * @param firstId 第一次聊天id
     * @param limit 聊天条数
     * @return 聊天列表
     */
    public R<Object> listMessage(String conversationId, String chatUserId, String firstId, Integer limit) {
        String s2 = "v1/messages";

        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put(CONVERSATION_ID, conversationId);
        messageMap.put("user", chatUserId);
        messageMap.put(LIMIT, limit);
        if (!"".equals(firstId)) {
            messageMap.put("first_id", firstId);
        }
        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.GET)
                .form(messageMap)
                .header(Header.AUTHORIZATION, authorization)
                .timeout(TIMEOUT)
                .execute().body();

        JSONObject jsonObject = JSONObject.parseObject(response);
        Map<String, Object> map = (Map<String, Object>) jsonObject;
        if (Boolean.TRUE.equals(checkCode(map.get(STATUS)))) {
            log.error(ERR_DIFY, map.toString());
            throw new ChatException(TOO_MUCH);
        }
        ListMessagesDto listMessagesDTO = JSONObject.parseObject(JSON.toJSONString(map), ListMessagesDto.class);
        return R.ok(listMessagesDTO);
    }

    /**
     * 聊天会话列表（已弃用）
     *
     * @param userId 用户id
     * @param lastId 最后一次聊天id
     * @param limit 聊天条数
     * @return 聊天信息
     */
    public R<Object> conversations(Long userId, String lastId, Integer limit) {
        String s2 = "v1/conversations";

        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("user", String.valueOf(userId));
        messageMap.put("last_id", lastId);
        messageMap.put(LIMIT, limit);
        String request = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.GET)
                .form(messageMap)
                .header(Header.AUTHORIZATION, authorization)
                .timeout(TIMEOUT)
                .execute().body();

        JSONObject jsonObject = JSONObject.parseObject(request);
        Map<String, Object> map = (Map<String, Object>) jsonObject;
        if (Boolean.TRUE.equals(checkCode(map.get(STATUS)))) {
            log.error(ERR_DIFY, map.toString());
            throw new ChatException(TOO_MUCH);
        }

        ListConversationsDto listConversationsDto = JSONObject.parseObject(JSON.toJSONString(map), ListConversationsDto.class);
        return R.ok(listConversationsDto);
    }

    /**
     * 校验接口是否出错
     *
     * @param status 状态
     */
    public Boolean checkCode(Object status) {
        if (Optional.ofNullable(status).isPresent()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
