package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.db.entity.ProjectFeedbackMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <P>
 * 针对表【project_feedback_message(反馈消息表)】的数据库操作Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Mapper
public interface ProjectFeedbackMessageMapper extends BaseMapper<ProjectFeedbackMessage> {

    /**
     * 按反馈 ID 和类型获取最旧一条消息
     *
     * @param feedbackId  反馈 ID
     * @param triggerType 触发器类型
     * @return {@link ProjectFeedbackMessage}
     */
    ProjectFeedbackMessage getByFeedbackIdAndType(@Param("feedbackId") Long feedbackId, @Param("triggerType") Integer triggerType);
}




