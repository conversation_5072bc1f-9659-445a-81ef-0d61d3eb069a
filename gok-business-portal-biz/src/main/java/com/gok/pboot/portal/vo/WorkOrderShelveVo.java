package com.gok.pboot.portal.vo;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单管理--搁置界面VO
 *
 * <AUTHOR>
 * @since 2023-08-29
 */

@Data
public class WorkOrderShelveVo {
    /**
     * 回复内容
     */
    private String responseContent;

    /**
     * 回复相关图片
     */
    private List<String> responseFileIds;

    /**
     * 指派处理人Id
     */
    private Long handleWorkerId;

    /**
     * 指派处理人员
     */
    private String handleWorker;

    /**
     * 反馈人
     */
    private String createBy;

    /**
     * 反馈人ID
     */
    private Long feedbackUserId;

    /**
     * 部门
     */
    private String dept;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈类型中文
     */
    private String typeStr;

    /**
     * 反馈时间
     */
    private LocalDateTime createTime;

    /**
     * 涉及系统
     */
    private String applicationName;

    /**
     * 反馈相关图片
     */
    private List<String> feedbackFileIds;

    /**
     * 批转信息
     */
    private List<ProjectFeedbackLogVo> logVos;
}
