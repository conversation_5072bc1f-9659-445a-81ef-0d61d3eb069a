package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 项目反馈详细vo
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeedbackDetailVo {

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型id（0:优化建议、1：功能异常、2:其他）
     */
    private Integer type;

    /**
     * 反馈类型Str
     */
    private String typeStr;
    /**
     * 系统
     */
    private String applicationName;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 处理状态（0待处理，1处理中，2取消，3搁置，4已解决，5已关闭）
     */
    private String handlingSituation;

    /**
     * 处理状态Str
     */
    private String handlingSituationStr;

    /**
     * 相关图片
     */
    private List<String> picture;

    /**
     * 项目反馈批转信息vo列表
     */
    private List<ProjectFeedbackLogVo> ProjectFeedbackLogVoList;

    /**
     * 项目反馈回复
     */
    private ProjectFeedbackResponseVo projectFeedbackResponseVo;

    /**
     * 评价
     */
    private Integer evaluated;

}
