package com.gok.pboot.portal.vo;

import com.gok.pboot.common.core.exception.BusinessException;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Optional;

/**
 * CMS 公文计数 VO
 *
 * <AUTHOR>
 * @date 17/1/2024
 */

@Data
public class CmsDocCountVo {

    /**
     * 全部
     */
    private Integer all;

    /**
     * 制度
     */
    private Integer systems;

    /**
     * 标准
     */
    private Integer standards;

    /**
     * 流程
     */
    private Integer processes;

    /**
     * 机制
     */
    private Integer mechanisms;

    /**
     * 计划
     */
    private Integer plans;

    /**
     * 通报
     */
    private Integer notifications;

    /**
     * 决定
     */
    private Integer decision;

    /**
     * CMS 文档计数 VO
     *
     * @param list 列表
     */
    public CmsDocCountVo(List<CmsSortCountVo> list) {
        // 获取CmsDocCountVo类中的所有字段，包括私有字段
        Field[] fields = this.getClass().getDeclaredFields();

        for (Field field : fields) {
            // 获取字段名
            String fieldName = field.getName();

            // 在CmsSortCountVo列表中查找与字段名相同的businessType
            Optional<CmsSortCountVo> opt = list.stream()
                    .filter(vo -> vo.getBusinessType().equals(fieldName))
                    .findFirst();

            // 如果找到了，将count赋值给当前类属性
            if (opt.isPresent()) {
                try {
                    // 设置字段可访问
                    field.setAccessible(true);

                    field.set(this, opt.get().getCount());
                } catch (IllegalAccessException e) {
                    // 异常处理
                    throw new BusinessException("CmsDocCountVo构建失败");
                }
            }
        }
    }
}
