package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.impl.SysFileServiceImpl;
import com.gok.pboot.common.core.util.DateUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.PortalBackground;
import com.gok.pboot.portal.db.entity.PortalHrmResource;
import com.gok.pboot.portal.db.mapper.PortalBackgroundMapper;
import com.gok.pboot.portal.dto.PortalBackgroundDto;
import com.gok.pboot.portal.dto.PortalBackgroundSaveOrUpdateDto;
import com.gok.pboot.portal.enums.BackgroundTypeEnum;
import com.gok.pboot.portal.enums.LaunchStatusEnum;
import com.gok.pboot.portal.enums.SuitTypeEnum;
import com.gok.pboot.portal.enums.ThumbnailEnum;
import com.gok.pboot.portal.service.PortalBackgroundService;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.util.EnumUtils;
import com.gok.pboot.portal.vo.PortalBackgroundDetailVo;
import com.gok.pboot.portal.vo.PortalBackgroundVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/08/03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PortalBackgroundServiceImpl extends ServiceImpl<PortalBackgroundMapper, PortalBackground>
        implements PortalBackgroundService {

    private final HomePageServiceImpl homePageService;

    private final SysFileServiceImpl sysFileService;

    private final CdnImgUtils cdnImgUtils;

    @Override
    public Page<PortalBackgroundVo> findPage(Page page, PortalBackgroundDto request) {
        // 查询分页对象，重新封装为Vo返回
        Page<PortalBackground> pageRes = baseMapper.findPage(page, request);
        List<PortalBackground> records = pageRes.getRecords();
        Page<PortalBackgroundVo> voPage = new Page<>();
        BeanUtil.copyProperties(pageRes, voPage, "records");

        if (CollUtil.isEmpty(pageRes.getRecords())) {
            return voPage;
        }

        List<PortalBackgroundVo> voList = getPortalBackgroundVos(pageRes, records);

        voPage.setTotal(pageRes.getTotal());
        voPage.setRecords(voList);
        return voPage;
    }

    /**
     * 获取门户背景分页对象 Vos
     *
     * @param pageRes 页面res
     * @param records 记录
     * @return {@link List}<{@link PortalBackgroundVo}>
     */
    @NotNull
    private List<PortalBackgroundVo> getPortalBackgroundVos(Page<PortalBackground> pageRes, List<PortalBackground> records) {
        // 关联查询sys_file表
        // 通过门户背景文件id列表获取门户背景文件Map
        QueryWrapper<SysFile> portalWrapper = new QueryWrapper<>();
        portalWrapper.in("id", CollUtil.getFieldValues(records, "portalFileId"));
        Map<Long, SysFile> portalFileMap = sysFileService.getFIleMap(portalWrapper);
        // 通过个人背景id列表获取个人背景文件Map
        QueryWrapper<SysFile> personWrapper = new QueryWrapper<>();
        personWrapper.in("id", CollUtil.getFieldValues(records, "personFileId"));
        Map<Long, SysFile> personFileMap = sysFileService.getFIleMap(personWrapper);
        // 通过应用背景id列表获取应用背景文件Map
        QueryWrapper<SysFile> appWrapper = new QueryWrapper<>();
        appWrapper.in("id", CollUtil.getFieldValues(records, "appFileId"));
        Map<Long, SysFile> appFileMap = sysFileService.getFIleMap(appWrapper);

        // 构建分页返回对象
        List<PortalBackgroundVo> voList = new ArrayList<>();
        pageRes.getRecords().forEach(r -> {
            PortalBackgroundVo voItem = BeanUtil.copyProperties(r, PortalBackgroundVo.class);
            voItem.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, voItem.getStatus()));
            SysFile portalFile = portalFileMap.get(r.getPortalFileId());
            if (Optional.ofNullable(portalFile).isPresent()) {
                voItem.setPortalFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(portalFile.getFileUrl()), ThumbnailEnum.PORTAL_IMAGE.getValue()));
            }
            SysFile personFile = personFileMap.get(r.getPersonFileId());
            if (Optional.ofNullable(personFile).isPresent()) {
                voItem.setPersonFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(personFile.getFileUrl()), ThumbnailEnum.PERSON_IMAGE.getValue()));
            }
            SysFile appFile = appFileMap.get(r.getAppFileId());
            if (Optional.ofNullable(appFile).isPresent()) {
                voItem.setAppFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(appFile.getFileUrl()), ThumbnailEnum.PERSON_IMAGE.getValue()));
            }
            voList.add(voItem);
        });
        return voList;
    }

    @Override
    public PortalBackgroundDetailVo findDetail(Long backgroundId) {
        PortalBackground portalBackground = baseMapper.selectById(backgroundId);

        if (!Optional.ofNullable(portalBackground).isPresent()) {
            return null;
        }

        return getPortalBackgroundDetailVo(portalBackground);
    }

    @Override
    public R<PortalBackgroundVo> find() {
        PortalHrmResource portalHrmResource = homePageService.getCurrentUser();

        PortalBackground portalBackground;
        PortalBackground birthdayBackground = null;
        PortalBackground anniversaryBackground = null;
        // 设置默认背景
        portalBackground = baseMapper.find(null, null, BackgroundTypeEnum.DEFAULT.getValue());

        if (!Optional.ofNullable(portalHrmResource).isPresent()) {
            // 取普通背景，有则替换默认背景
            PortalBackground normalBackground = baseMapper.findNormal(null);
            if (Optional.ofNullable(normalBackground).isPresent()) {
                setAndValidateBackground(portalBackground, normalBackground);
                portalBackground = normalBackground;
            }
            if (!Optional.ofNullable(portalBackground).isPresent()) {
                return R.failed("当前无适用背景");
            }
            return R.ok(getPortalBackgroundVo(portalBackground));
        }

        // 判断当日是否是用户生日或入职纪念日
        // 查询个人生日适用背景（门户背景、个人背景不存在时设置为默认背景）
        DateUtils dateUtils = new DateUtils();
        if (dateUtils.toLocalDateAndEqual(portalHrmResource.getBirthday())) {
            birthdayBackground = baseMapper.find(SuitTypeEnum.SUIT.getValue(), null, null);
            setAndValidateBackground(portalBackground, birthdayBackground);
        }
        // 查询入职周年适用背景（门户背景、个人背景不存在时设置为默认背景）
        if (dateUtils.toLocalDateAndEqual(portalHrmResource.getStartDate())) {
            anniversaryBackground = baseMapper.find(null, SuitTypeEnum.SUIT.getValue(), null);
            setAndValidateBackground(portalBackground, anniversaryBackground);
        }

        // 设置适配背景（个人生日、入职周年适用背景 优先于 默认背景，发生互斥时按权重，权重一样按创建时间）
        if (Optional.ofNullable(birthdayBackground).isPresent() && Optional.ofNullable(anniversaryBackground).isPresent()) {
            portalBackground = getAndCompare(birthdayBackground, anniversaryBackground);
        } else if (Optional.ofNullable(birthdayBackground).isPresent()) {
            portalBackground = birthdayBackground;
        } else if (Optional.ofNullable(anniversaryBackground).isPresent()) {
            portalBackground = anniversaryBackground;
        } else {
            // 当前日期不是个人生日、入职周年时，取权重更高的普通背景，有则替换默认背景
            Integer weight = Optional.ofNullable(portalBackground).isPresent() ? portalBackground.getWeight() : 0;
            PortalBackground normalBackground = baseMapper.findNormal(weight);
            if (Optional.ofNullable(normalBackground).isPresent()) {
                setAndValidateBackground(portalBackground, normalBackground);
                portalBackground = normalBackground;
            }
        }

        if (!Optional.ofNullable(portalBackground).isPresent()) {
            return R.failed("当前无适用背景");
        }

        return R.ok(getPortalBackgroundVo(portalBackground));
    }

    /**
     * 设置和验证背景
     *
     * @param portalBackground   门户网站背景
     * @param birthdayBackground 生日背景
     */
    private static void setAndValidateBackground(PortalBackground portalBackground, PortalBackground birthdayBackground) {
        if (!Optional.ofNullable(portalBackground).isPresent()) {
            return;
        }
        //赋值PortalFileId
        if (Optional.ofNullable(birthdayBackground).isPresent() &&
                !Optional.ofNullable(birthdayBackground.getPortalFileId()).isPresent()) {
            birthdayBackground.setPortalFileId(portalBackground.getPortalFileId());
        }
        //赋值PersonFileId
        if (Optional.ofNullable(birthdayBackground).isPresent() &&
                !Optional.ofNullable(birthdayBackground.getPersonFileId()).isPresent()) {
            birthdayBackground.setPersonFileId(portalBackground.getPersonFileId());
        }
        //赋值appFileId
        if (Optional.ofNullable(birthdayBackground).isPresent() &&
                !Optional.ofNullable(birthdayBackground.getAppFileId()).isPresent()) {
            birthdayBackground.setAppFileId(portalBackground.getAppFileId());
        }
    }

    /**
     * 获取和比较生日背景、周年背景的优先级
     *
     * @param birthdayBackground    生日背景
     * @param anniversaryBackground 周年背景
     * @return {@link PortalBackground}
     */
    @NotNull
    private static PortalBackground getAndCompare(PortalBackground birthdayBackground, PortalBackground anniversaryBackground) {
        // 比较权重
        Integer birthdayBackgroundWeight = birthdayBackground.getWeight();
        Integer anniversaryBackgroundWeight = anniversaryBackground.getWeight();
        if (!birthdayBackgroundWeight.equals(anniversaryBackgroundWeight)) {
            return birthdayBackgroundWeight > anniversaryBackgroundWeight
                    ? birthdayBackground : anniversaryBackground;
        }

        // 权重相等比较创建时间
        LocalDateTime birthdayBackgroundCreateTime = birthdayBackground.getCreateTime();
        LocalDateTime anniversaryBackgroundCreateTime = anniversaryBackground.getCreateTime();
        if (Optional.ofNullable(birthdayBackgroundCreateTime).isPresent()) {
            return birthdayBackgroundCreateTime.isAfter(anniversaryBackgroundCreateTime)
                    ? birthdayBackground : anniversaryBackground;
        } else {
            return anniversaryBackground;
        }
    }

    /**
     * 获取门户背景 Vo
     *
     * @param portalBackground 门户背景
     * @return {@link PortalBackgroundVo}
     */
    @NotNull
    private PortalBackgroundVo getPortalBackgroundVo(PortalBackground portalBackground) {
        PortalBackgroundVo portalBackgroundVo = BeanUtil.copyProperties(portalBackground, PortalBackgroundVo.class);
        portalBackgroundVo.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, portalBackgroundVo.getStatus()));


        // 关联查询sys_file表
        // 获取并设置个人背景文件url
        Long personFileId = portalBackgroundVo.getPersonFileId();
        if (Optional.ofNullable(personFileId).isPresent()) {
            SysFile sysFile = sysFileService.getById(personFileId);
            if (Optional.ofNullable(sysFile).isPresent()) {
                portalBackgroundVo.setPersonFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(sysFile.getFileUrl()), ThumbnailEnum.PERSON_IMAGE.getValue()));
            }
        }
        // 获取并设置门户背景文件url
        Long portalFileId = portalBackgroundVo.getPortalFileId();
        if (Optional.ofNullable(portalFileId).isPresent()) {
            SysFile sysFile = sysFileService.getById(portalFileId);
            if (Optional.ofNullable(sysFile).isPresent()) {
                portalBackgroundVo.setPortalFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(sysFile.getFileUrl()), ThumbnailEnum.PORTAL_IMAGE.getValue()));
            }
        }
        // 获取并设置应用背景文件url
        Long appFileId = portalBackgroundVo.getAppFileId();
        if (Optional.ofNullable(appFileId).isPresent()) {
            SysFile sysFile = sysFileService.getById(appFileId);
            if (Optional.ofNullable(sysFile).isPresent()) {
                portalBackgroundVo.setAppFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(sysFile.getFileUrl()), ThumbnailEnum.PORTAL_IMAGE.getValue()));
            }
        }
        return portalBackgroundVo;
    }

    /**
     * 获取门户背景详情 Vo
     *
     * @param portalBackground 门户网站背景
     * @return {@link PortalBackgroundDetailVo}
     */
    @NotNull
    private PortalBackgroundDetailVo getPortalBackgroundDetailVo(PortalBackground portalBackground) {
        PortalBackgroundDetailVo portalBackgroundDetailVo = BeanUtil.copyProperties(portalBackground, PortalBackgroundDetailVo.class);
        portalBackgroundDetailVo.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, portalBackgroundDetailVo.getStatus()));

        // 关联查询sys_file表
        // 获取并设置个人背景文件url
        Long personFileId = portalBackgroundDetailVo.getPersonFileId();
        if (Optional.ofNullable(personFileId).isPresent()) {
            SysFile sysFile = sysFileService.getById(personFileId);
            if (Optional.ofNullable(sysFile).isPresent()) {
                portalBackgroundDetailVo.setPersonFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(sysFile.getFileUrl()), ThumbnailEnum.PERSON_IMAGE.getValue()));
            }
        }
        // 获取并设置门户背景文件url
        Long portalFileId = portalBackgroundDetailVo.getPortalFileId();
        if (Optional.ofNullable(portalFileId).isPresent()) {
            SysFile sysFile = sysFileService.getById(portalFileId);
            if (Optional.ofNullable(sysFile).isPresent()) {
                portalBackgroundDetailVo.setPortalFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(sysFile.getFileUrl()), ThumbnailEnum.PORTAL_IMAGE.getValue()));
            }
        }
        // 获取并设置应用背景文件url
        Long appFileId = portalBackgroundDetailVo.getAppFileId();
        if (Optional.ofNullable(appFileId).isPresent()) {
            SysFile sysFile = sysFileService.getById(appFileId);
            if (Optional.ofNullable(sysFile).isPresent()) {
                portalBackgroundDetailVo.setAppFileUrl(cdnImgUtils.getThumbnail(cdnImgUtils.createHttpUrl(sysFile.getFileUrl()), ThumbnailEnum.PORTAL_IMAGE.getValue()));
            }
        }
        return portalBackgroundDetailVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(PortalBackgroundSaveOrUpdateDto request) {
        PortalBackground entity = PortalBackground.buildSave(request);
        baseMapper.insert(entity);
        Long backgroundId = entity.getId();

        return backgroundId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(PortalBackgroundSaveOrUpdateDto request) {
        Long backgroundId = request.getId();
        Assert.isTrue(Optional.ofNullable(backgroundId).isPresent(), "背景id不能为空");

        PortalBackground entity = PortalBackground.buildUpdate(request);
        baseMapper.updateById(entity);

        return backgroundId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateStatus(Long backgroundId, Integer status) {
        Assert.isTrue(LaunchStatusEnum.isValueLegal(status), "状态字段非法");

        PortalBackground portalBackground = baseMapper.selectById(backgroundId);
        Assert.isTrue(Optional.ofNullable(portalBackground).isPresent(), "门户背景不存在");
        baseMapper.updateStatusById(backgroundId, status, SecurityUtils.getUser().getUsername(), DateUtil.now());

        return backgroundId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchDelete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            log.warn("内容id集合为空,批量删除操作结束！");
            return ids;
        }
        baseMapper.logicDeleteByIdList(ids);
        return ids;
    }
}
