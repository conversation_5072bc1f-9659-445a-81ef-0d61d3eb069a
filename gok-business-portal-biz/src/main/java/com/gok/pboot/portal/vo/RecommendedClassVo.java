package com.gok.pboot.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推荐课程 Vo类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendedClassVo {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 跳转河狸url
     */
    private String linkAddress;

    /**
     * 河狸对应的appId
     */
    private Long belongApplicationId;

    /**
     * 班课id
     */
    private Long classId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 类型  班课=0、慕课=1
     */
    private Integer classType;

    /**
     * 课程背景图片
     */
    private String imagUrl;

    /**
     * 课程名称
     */
    private String name;

}
