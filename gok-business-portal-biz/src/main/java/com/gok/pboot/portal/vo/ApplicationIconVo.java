package com.gok.pboot.portal.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 应用图标Vo
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationIconVo {

    /**
     * 原本父级分类id
     */
    private Long categoryId;

    /**
     * 应用id
     */
    private Long applicationId;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件url
     */
    private String fileUrl;

    /**
     * 跳转链接
     */
    private String linkAddress;

    /**
     * 所属应用id
     */
    private Long belongApplicationId;

    /**
     * 应用程序图标 VO
     *
     * @param applicationId   应用程序 ID
     * @param applicationName 应用程序名称
     */
    public ApplicationIconVo(Long applicationId, String applicationName) {
        this.applicationId = applicationId;
        this.applicationName = applicationName;
    }
}
