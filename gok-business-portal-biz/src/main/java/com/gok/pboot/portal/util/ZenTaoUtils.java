package com.gok.pboot.portal.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.gok.pboot.portal.common.DBApi;
import com.gok.pboot.portal.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 禅道工具
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Slf4j
@Component
public class ZenTaoUtils {

    @Value("${zen-tao.goktech}")
    private String s1;

    /**
     * 账号
     */
    @Value("${zen-tao.account}")
    private String account;

    /**
     * 密码
     */
    @Value("${zen-tao.password}")
    private String password;

    @Autowired
    private DBApi dbApi;

    /**
     * 超时时间
     */
    public static final Integer TIMEOUT = 20000;

    /**
     * token
     */
    public static final String TOKEN = "Token";

    /**
     * @return token
     */
    public String createToken() {
        String s2 = "v1/tokens";

        Map<String, Object> userMap = new HashMap<>();
        userMap.put("account", account);
        userMap.put("password", password);

        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.POST)
                .body(JSONObject.toJSONString(userMap))
                .timeout(TIMEOUT)
                .execute().body();
        JSONObject jsonObject = JSONObject.parseObject(response);
        Map<String, Object> map = (Map<String, Object>) jsonObject;

        return String.valueOf(map.get("token"));
    }

    /**
     * 创建BUG
     *
     * @param productId 产品id
     * @param title 标题
     * @param severity 严重程度
     * @param priority 优先级
     * @param type 类型
     * @param userId 指派给某人（中台人员id）
     * @param steps 重现步骤
     */
    public Map<String, Object> createBug(Integer productId, String title, Integer severity, Integer priority, String type, Long userId, String steps) {
        String s2 = "v1/products/" + productId + "/bugs";

        Map<String, Object> bugMap = new HashMap<>();
        if (title.length() > 100) {
            title = title.substring(0, 100) + "...";
        }
        bugMap.put("title", "【用户反馈】" + title);
        bugMap.put("severity", severity);
        bugMap.put("pri", priority);
        bugMap.put("type", type);
        // 根据中台id获取人员名称
        log.info("指派人的中台id: {}", userId);
        List<UserVo> userVoList = dbApi.getUserIdByZenTao(userId);
        if (CollUtil.isNotEmpty(userVoList)) {
            bugMap.put("assignedTo", userVoList.get(NumberUtils.INTEGER_ZERO).getLoginId());
        }
        bugMap.put("assignedDate", DateUtil.now());
        bugMap.put("status","active");
        bugMap.put("steps", steps);
        bugMap.put("openedBuild", "trunk");
        log.info("zentao createBug:::" + bugMap.toString());
        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.POST)
                .header(TOKEN, createToken())
                .body(JSONObject.toJSONString(bugMap))
                .timeout(TIMEOUT)
                .execute().body();

        Map<String, Object> map = (Map<String, Object>) JSONObject.parseObject(response);
        return map;
    }

    /**
     * 获取Bug详情
     *
     * @param id Bug id
     * @return Bug详情
     */
    public Map<String, Object> getBugDetail(Long id) {
        String s2 = "v1/bugs/" + id;

        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.GET)
                .header(TOKEN, createToken())
                .execute().body();

        return (Map<String, Object>) JSONObject.parseObject(response);
    }

    /**
     * 获取需求详情
     *
     * @param id 需求 id
     * @return 需求详情
     */
    public Map<String, Object> getStoreDetail(Long id) {
        String s2 = "v1/stories/" + id;

        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.GET)
                .header(TOKEN, createToken())
                .execute().body();

        return (Map<String, Object>) JSONObject.parseObject(response);
    }

    /**
     * 激活bug
     *
     * @param id 编号
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String, Object> activeBug(Long id, Long userId) {
        String s2 = "v1/bugs/" + id + "/active";

        // 根据中台id获取人员名称
        Map<String, Object> bugMap = new HashMap<>();
        log.info("指派人的中台id: {}", userId);
        List<UserVo> userVoList = dbApi.getUserIdByZenTao(userId);
        if (CollUtil.isNotEmpty(userVoList)) {
            bugMap.put("assignedTo", userVoList.get(NumberUtils.INTEGER_ZERO).getLoginId());
        }

        String response = HttpRequest.of(s1 + s2, StandardCharsets.UTF_8)
                .method(Method.POST)
                .header(TOKEN, createToken())
                .body(JSONObject.toJSONString(bugMap))
                .timeout(TIMEOUT)
                .execute().body();

        return (Map<String, Object>) JSONObject.parseObject(response);
    }

}
