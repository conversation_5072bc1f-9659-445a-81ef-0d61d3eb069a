package com.gok.pboot.portal.vo;

import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Data;


/**
 * VOD VO
 *
 * <AUTHOR>
 * @Description :  视频auth信息
 * @date 2024/01/22
 */
@Data
public class VodVo {

    /**
     *  华为的视频key
     */
    @StringVerify(name="",required = true)
    private String assetId;

    /**
     * 华为 通过服务端创建媒资返回的文件上传路径
     */
    private String object;

    /**
     * 通过服务端创建媒资返回的文件上传路径
     */
    private String bucket;

    /**
     * 通过服务端创建媒资返回的region信息
     */
    private String location;

}
