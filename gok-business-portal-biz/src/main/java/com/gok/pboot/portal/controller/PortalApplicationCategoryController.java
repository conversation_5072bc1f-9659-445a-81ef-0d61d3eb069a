package com.gok.pboot.portal.controller;

import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.PortalApplicationCategoryDto;
import com.gok.pboot.portal.dto.PortalApplicationCategoryDtoItem;
import com.gok.pboot.portal.service.IPortalApplicationCategoryService;
import com.gok.pboot.portal.vo.PortalApplicationCategoryVo;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 应用分类表
 * <AUTHOR>
 * @description 应用分类表
 * @menu 应用分类
 * @since 2023-08-01
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/portalApplicationCategory")
@Api(tags = "应用分类表")
public class PortalApplicationCategoryController {

    @Autowired
    private IPortalApplicationCategoryService portalApplicationCategoryService;


    /**
     * 查询
     *
     * @return 列表
     */
    @GetMapping("/list")
    public R<List<PortalApplicationCategoryVo>> list() {
        return R.ok(portalApplicationCategoryService.getAllCategory());
    }

    /**
     * 编辑
     *
     * @param dto 编辑结束后传输给后端数据列表
     * @return R
     */
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody PortalApplicationCategoryDto dto) {
        return portalApplicationCategoryService.editList(dto);
    }

}
