package com.gok.pboot.portal.util;

import com.gok.module.upms.common.enums.ValueEnum;

import java.util.Optional;

/**
 * 枚举工具类
 * 后期迁移至common
 *
 * <AUTHOR>
 * @since 2023-08-01
 **/
public class EnumUtils {

    private EnumUtils(){

    }

    /**
     * 按值获取名称
     *
     * @param enumClass 枚举类模板
     * @param value     枚举值
     * @return java.lang.String
     * @description 根据名称获取值
     * <AUTHOR>
     * @date 2022/7/22 9:44
     */
    public static <E extends Enum<? extends ValueEnum<V>>, V> String getNameByValue(Class<E> enumClass, V value) {
        ValueEnum<V> nameValueEnum;
        if (!Optional.ofNullable(value).isPresent()) {
            return null;
        }

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()) {
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getValue().equals(value)) {
                return nameValueEnum.getName();
            }
        }

        return null;
    }

    /**
     * 按名称获取值
     *
     * @param enumClass 枚举类模板
     * @param name      枚举名称
     * @return V
     * @description 根据枚举名称获取枚举值
     * <AUTHOR>
     * @date 2022/7/22 9:45
     */
    public static <E extends Enum<? extends ValueEnum<V>>, V> V getValueByName(Class<E> enumClass, String name) {
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()) {
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getName().equals(name)) {
                return nameValueEnum.getValue();
            }
        }

        return null;
    }

    /**
     * ~ 根据名称获取枚举 ~
     *
     * @param enumClass 枚举类模板
     * @param name      枚举名称
     * @return com.gok.pboot.enumeration.ValueEnum<V>
     * <AUTHOR>
     * @date 2022/7/28 9:34
     */
    public static <E extends Enum<? extends ValueEnum<V>>, V> E getEnumByName(
            Class<E> enumClass, String name
    ) {
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()) {
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getName().equals(name)) {
                return (E) nameValueEnum;
            }
        }

        return null;
    }

    /**
     * ~ 根据值获取枚举 ~
     *
     * @param enumClass 枚举类模板
     * @param value     枚举值
     * @return com.gok.pboot.enumeration.ValueEnum<V>
     * <AUTHOR>
     * @date 2022/7/28 9:34
     */
    public static <E extends Enum<? extends ValueEnum<V>>, V> E getEnumByValue(
            Class<E> enumClass, V value
    ) {
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()) {
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getValue().equals(value)) {
                return (E) nameValueEnum;
            }
        }

        return null;
    }

    /**
     * 值等于
     *
     * @param value   价值
     * @param enumObj 枚举 obj
     * @return boolean
     */
    public static <V> boolean valueEquals(V value, ValueEnum<V> enumObj) {
        return enumObj.getValue().equals(value);
    }

    /**
     * 存在枚举值
     *
     * @param value 价值
     * @param clazz 克拉兹
     * @return boolean
     */
    public static <E extends Enum<? extends ValueEnum<V>>, V> boolean existsEnumValue(V value, Class<E> clazz) {
        ValueEnum<V> nameValueEnum;

        for (E e : clazz.getEnumConstants()) {
            nameValueEnum = ((ValueEnum<V>) e);
            if (nameValueEnum.getValue().equals(value)) {
                return true;
            }
        }

        return false;
    }

}
