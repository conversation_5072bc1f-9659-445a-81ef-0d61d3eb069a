package com.gok.pboot.portal.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 门户首页-个人信息
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoVo {
    /**
     * id
     */
    private Long id;

    /**
     * 姓名
     */
    private String aliasName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 部门
     */
    private String deptName;

    /**
     * oa工号
     */
    private String workCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 入职时间
     */
    private Date startDate;

    /**
     * 入职天数
     */
    private Long jobDays;

    /**
     * 职位
     */
    private String job;

    /**
     * 职级
     */
    private String positionLevel;

    /**
     * 职级 字典值
     */
    private String positionLevelTxt;

    /**
     * 用户名
     */
    private String usrName;
}
