package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 适用类型枚举类
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Getter
public enum SuitTypeEnum implements ValueEnum<Integer> {

    /**
     * 不适用
     */
    NOT_SUIT(0, "不适用"),

    /**
     * 适用
     */
    SUIT(1, "适用");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    SuitTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
