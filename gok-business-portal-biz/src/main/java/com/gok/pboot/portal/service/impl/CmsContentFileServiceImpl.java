package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.impl.SysFileServiceImpl;
import com.gok.pboot.portal.db.entity.CmsContent;
import com.gok.pboot.portal.db.entity.CmsContentFile;
import com.gok.pboot.portal.db.mapper.CmsContentFileMapper;
import com.gok.pboot.portal.db.mapper.CmsContentMapper;
import com.gok.pboot.portal.dto.CmsContentFileUploadDto;
import com.gok.pboot.portal.dto.CmsContentFileUploadDtoItem;
import com.gok.pboot.portal.enums.FileTypeEnum;
import com.gok.pboot.portal.enums.LaunchStatusEnum;
import com.gok.pboot.portal.service.CmsContentAuthService;
import com.gok.pboot.portal.service.ICmsContentFileService;
import com.gok.pboot.portal.util.EnumUtils;
import com.gok.pboot.portal.vo.CmsContentDetailVo;
import com.gok.pboot.portal.vo.CmsContentFileVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 内容附件 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-03
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CmsContentFileServiceImpl extends ServiceImpl<CmsContentFileMapper, CmsContentFile> implements ICmsContentFileService {

    private final SysFileServiceImpl sysFileService;

    private final CmsContentMapper cmsContentMapper;

    private final CmsContentAuthService cmsContentAuthService;


    @Override
    public Page<CmsContentFileVo> findPage(Page page, Long contentId) {
        CmsContent cmsContent = cmsContentMapper.selectById(contentId);
        if (!Optional.ofNullable(cmsContent).isPresent() ||
                Boolean.FALSE.equals(cmsContentAuthService.isAuthByCmsId(contentId, cmsContent.getSendObj())) ||
                LocalDateTime.now().isBefore(cmsContent.getPublishTime()) ||
                LaunchStatusEnum.REMOVAL.getValue().equals(cmsContent.getStatus())) {
            //没有权限或者是发布时间在现在之后或者下架
            return new Page();
        }

        Page<CmsContentFile> pageResult = baseMapper.findPage(page, contentId);
        List<CmsContentFile> records = pageResult.getRecords();
        Page<CmsContentFileVo> voPage = BeanUtil.copyProperties(pageResult, Page.class, "records");
        if (CollUtil.isEmpty(records)) {
            return voPage;
        }

        // 实体类关联sys_file表获取信息后转Vo类
        List<CmsContentFileVo> voList = covert2CmsContentFileVoList(records);
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchUpload(CmsContentFileUploadDto request) {
        Long contentId = request.getContentId();

        // 1. 内容附件集合为空 则逻辑删除所有附件
        List<CmsContentFileUploadDtoItem> fileList = request.getFileList();
        if (CollUtil.isEmpty(fileList)) {
            baseMapper.logicDeleteByContentId(contentId);
            return new ArrayList<>();
        }

        // 2. 内容附件集合不为空，根据内容id获取已存在的内容附件集合
        List<CmsContentFile> entityList = new ArrayList<>();
        List<Long> resultIdList = new ArrayList<>();
        List<CmsContentFile> existEntityList = baseMapper.selByContentIdList(Arrays.asList(contentId));
        // 2.1 若已存在内容附件信息，按需处理附件信息
        //  2.1.1. id在request和db中都存在 则默认同一文件 不做处理
        //  2.1.2. 主键id为空 批量新增
        //  2.1.3. id在request中不存在 db中存在 则默认删除文件 批量逻辑删除
        if (CollUtil.isNotEmpty(existEntityList)) {
            // 获取上传请求中的文件id集合
            List<Long> requestIds = fileList.stream().map(CmsContentFileUploadDtoItem::getId).collect(Collectors.toList());
            // 获取数据库已存在的文件id集合
            List<Long> existIds = existEntityList.stream().map(CmsContentFile::getId).collect(Collectors.toList());

            // 获取request和db中都存在的文件集合 不做处理
            List<Long> intersectionIds = (List<Long>) CollUtil.intersection(existIds, requestIds);
            resultIdList.addAll(intersectionIds);

            // 主键id为空的 批量新增
            List<CmsContentFileUploadDtoItem> addFileList = fileList.stream().filter(f -> null == f.getId()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(addFileList)) {
                addFileList.forEach(r -> entityList.add(covert2CmsContentFile(contentId, r)));
            }

            // 获取request中不存在 db中存在的文件id集合 批量逻辑删除
            List<Long> delIds = CollUtil.subtractToList(existIds, requestIds);
            if (CollUtil.isNotEmpty(delIds)) {
                baseMapper.logicDeleteByIdList(delIds);
            }
        } else {
            // 2.2 若不存在内容附件信息，批量新增内容附件记录
            fileList.forEach(r -> entityList.add(covert2CmsContentFile(contentId, r)));
        }

        if (CollUtil.isNotEmpty(entityList)) {
            baseMapper.batchSave(entityList);
            resultIdList.addAll(entityList.stream()
                    .map(CmsContentFile::getId)
                    .collect(Collectors.toList()));
        }

        return resultIdList;
    }

    @Override
    public List<CmsContentFileVo> findByContentIdList(List<Long> contentIds) {
        if (CollUtil.isEmpty(contentIds)) {
            return new ArrayList<>();
        }

        return covert2CmsContentFileVoList(baseMapper.selByContentIdList(contentIds));
    }

    /**
     * 上传Dto类转为内容附件实体类
     *
     * @param contentId 内容id
     * @param request   dto请求实体
     * @return 内容附件实体
     */
    private CmsContentFile covert2CmsContentFile(Long contentId, CmsContentFileUploadDtoItem request) {
        if (!Optional.ofNullable(request).isPresent()) {
            return new CmsContentFile();
        }
        CmsContentFile entity = BeanUtil.copyProperties(request, CmsContentFile.class);
        entity.setContentId(contentId);
        return entity;
    }

    /**
     * 实体类转Vo类
     *
     * @param entity 内容附件实体
     * @return 内容附件vo
     */
    private CmsContentFileVo covert2CmsContentFileVo(CmsContentFile entity, Map<Long, SysFile> sysFileMap) {
        if (!Optional.ofNullable(entity).isPresent()) {
            return new CmsContentFileVo();
        }
        CmsContentFileVo vo = BeanUtil.copyProperties(entity, CmsContentFileVo.class);
        vo.setFileTypeTxt(EnumUtils.getNameByValue(FileTypeEnum.class, vo.getFileType()));

        if (Optional.ofNullable(entity.getFileId()).isPresent() && CollUtil.isNotEmpty(sysFileMap)) {
            SysFile sysFile = sysFileMap.getOrDefault(entity.getFileId(), new SysFile());
            vo.setFileSize(sysFile.getFileSize());
            vo.setOriginal(sysFile.getOriginal());
            vo.setFileName(sysFile.getFileName());
            vo.setFileUrl(sysFile.getFileUrl());
        }

        return vo;
    }

    /**
     * 内容附件实体类集合关联sys_file转为Vo类集合
     *
     * @param contentFileList 内容附件实体类集合[]
     * @return Vo类集合[]
     */
    private List<CmsContentFileVo> covert2CmsContentFileVoList(List<CmsContentFile> contentFileList) {
        List<CmsContentFileVo> voList = new ArrayList<>();
        if (CollUtil.isEmpty(contentFileList)) {
            return voList;
        }

        // 关联查询sys_file表，获取文件信息
        List<Long> fileIds = CollUtil.getFieldValues(contentFileList, "fileId", Long.class);
        List<SysFile> sysList = new ArrayList<>();
        if (CollUtil.isNotEmpty(fileIds)) {
            QueryWrapper<SysFile> wrapper = new QueryWrapper<>();
            wrapper.in("id", fileIds);
            sysList = sysFileService.list(wrapper);
        }

        // key-fileId  value-SysFile实体类
        Map<Long, SysFile> sysFileMap = CollUtil.isNotEmpty(sysList)
                ? sysList.parallelStream().collect(Collectors.toMap(SysFile::getId, Function.identity(), (a, b) -> a))
                : new HashMap<>();

        for (CmsContentFile file : contentFileList) {
            CmsContentFileVo voItem = covert2CmsContentFileVo(file, sysFileMap);
            voList.add(voItem);
        }
        return voList;
    }

}
