package com.gok.pboot.portal.service.impl;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysOauthClientDetailsVO;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.PortalApplicationRecent;
import com.gok.pboot.portal.dto.PortalApplicationRecentDto;
import com.gok.pboot.portal.service.PortalApplicationRecentService;
import com.gok.pboot.portal.db.mapper.PortalApplicationRecentMapper;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【portal_application_recent(应用中心-最近应用)】的数据库操作Service实现
* @createDate 2023-10-25 11:22:48
*/
@Service
public class PortalApplicationRecentServiceImpl extends ServiceImpl<PortalApplicationRecentMapper, PortalApplicationRecent>
    implements PortalApplicationRecentService{

    @Resource
    private CdnImgUtils cdnImgUtils;

    @Resource
    private RemoteOutService remoteOutService;

    @Value("${appId}")
    private Long appId;

    @Override
    public R saveRecent(PortalApplicationRecentDto req) {
        Long applicationId = req.getApplicationId();
        PigxUser pigxUser = SecurityUtils.getUser();
        Long userId = pigxUser.getId();
        //判断数据库中是否有数据，有则更新createTime的值
        if ( baseMapper.countByUserAppId(userId,applicationId) > 0 ){
            baseMapper.updateRecent(userId,applicationId);
            return R.ok();
        }
        PortalApplicationRecent entity = BeanUtil.copyProperties(req, PortalApplicationRecent.class);
        //基础字段赋值
        entity.setUserId(userId);
        entity.setTenantId(UserUtils.getUser().getTenantId());
        //入库
        long id = IdWorker.getId();
        entity.setId(id);
        this.save(entity);

        return R.ok(id);
    }

    @Override
    public R<List<ApplicationIconVo>> recentList(Long userId) {
        List<ApplicationIconVo> voList =  baseMapper.recentList(userId);
        List<SysOauthClientDetailsVO> data = remoteOutService.getAppListByUserId(UserUtils.getUser().getId(), appId).getData();
        List<ApplicationIconVo> mhLinkAddress = setMhLinkAddress(voList, data);
        return R.ok(mhLinkAddress);
    }



    /**
     * 给门户的应用赋予中台的地址
     * @param applicationIconVoList 门户的应用列表
     * @param sysOauthClientDetailsVOList 中台的应用列表
     * @return 修改后的门户应用列表
     */
    private List<ApplicationIconVo> setMhLinkAddress(List<ApplicationIconVo> applicationIconVoList,List<SysOauthClientDetailsVO>sysOauthClientDetailsVOList) {
        boolean flag = true;
        if (applicationIconVoList.isEmpty()) {
            return null;
        }
        Iterator<ApplicationIconVo> iterator = applicationIconVoList.iterator();

        while (iterator.hasNext()) {
            ApplicationIconVo iconVo = iterator.next();

            iconVo.setFileUrl(cdnImgUtils.createHttpUrl(iconVo.getFileUrl()));

            flag = true;

            for (SysOauthClientDetailsVO detailsVO : sysOauthClientDetailsVOList) {
                if (iconVo.getBelongApplicationId() != null && iconVo.getBelongApplicationId().equals(detailsVO.getId())) {
                    iconVo.setLinkAddress(detailsVO.getRedirectUrl());
                    flag = false;
                    break;
                }
            }

            if (flag) {
                // 如果含有非权限的应用，则使用迭代器的 remove 方法移除
                iterator.remove();
            }
        }

        return applicationIconVoList;
    }
}




