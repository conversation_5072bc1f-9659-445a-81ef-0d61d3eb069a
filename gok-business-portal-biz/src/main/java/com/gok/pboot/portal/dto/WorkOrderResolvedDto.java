package com.gok.pboot.portal.dto;


import lombok.Data;


/**
 * 已解决工单 DTO
 *
 * <AUTHOR>
 * @since 2023-08-29
 * 工单管理--已解决Dto
 */
@Data
public class WorkOrderResolvedDto {

    /**
     * 反馈ID
     */
    private Long feedbackId;

    /**
     * 反馈人
     */
    private String createBy;

    /**
     * 反馈人ID
     */
    private Long feedbackUserId;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 回复内容
     */
    private String responseContent;

    /**
     * 回复相关图片
     */
    private String responseFileIds;

    /**
     * 跟进人员ID
     */
    private Long followedWorkerId;

    /**
     * 跟进人
     */
    private String followedWorker;

    /**
     * 紧急程度
     */
    private Integer urgencyDegree;

    /**
     * 批转操作内容
     */
    private String operationContent;

}
