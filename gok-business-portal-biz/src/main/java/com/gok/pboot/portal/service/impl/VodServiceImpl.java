package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.config.HwYunVodConfig;
import com.gok.pboot.portal.db.entity.CmsContentFile;
import com.gok.pboot.portal.db.mapper.CmsContentFileMapper;
import com.gok.pboot.portal.db.mapper.CmsContentMapper;
import com.gok.pboot.portal.dto.CmsContentEditCoverUrlDto;
import com.gok.pboot.portal.dto.VodConfirmDto;
import com.gok.pboot.portal.service.VodService;
import com.gok.pboot.portal.util.AesUtils;
import com.gok.pboot.portal.vo.*;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.*;
import com.huaweicloud.sdk.iam.v3.region.IamRegion;
import com.huaweicloud.sdk.vod.v1.VodClient;
import com.huaweicloud.sdk.vod.v1.model.*;
import com.huaweicloud.sdk.vod.v1.region.VodRegion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description   :  文件上传
 * <AUTHOR>  luoyq
 * @date          :  2023/8/7 16:29
 * @menu        HuaWeiYun视频服务
 */
@Service
@Slf4j
public class VodServiceImpl implements VodService {

    @Resource
    private HwYunVodConfig hwyunVodConfig;

    @Resource
    private CmsContentFileMapper cmsContentFileMapper;

    @Resource
    private CmsContentMapper cmsContentMapper;

    private VodClient client = null;
    private ICredential auth = null;

    private final String assetInfoStatus = "NO_ASSET";

    /**
     * 华为云获取转码视频相关信息
     */
    private final String subject = "subject";
    private final String transcodeComplete = "transcodeComplete";
    private final String message = "message";
    private final String transcodeInfo = "transcode_info";
    private final String status = "status";
    private final String assetId = "asset_id";
    private final String subscribeUrl = "subscribe_url";

    @Autowired
    public VodServiceImpl(HwYunVodConfig hwyunVodConfig){
        this.hwyunVodConfig = hwyunVodConfig;
        init();
    }

    /**
     * 初始化VodClient
     */
    public void init() {
        try {
            auth = new BasicCredentials()
                    .withAk(hwyunVodConfig.getAccessKeyId())
                    .withSk(hwyunVodConfig.getAccessKeySecret());
            client = VodClient.newBuilder()
                    .withCredential(auth)
                    .withRegion(VodRegion.valueOf(hwyunVodConfig.getRegion()))
                    .build();
        } catch (Exception e) {
            log.info("初始化错误===={}", e.getMessage());
        }
    }

    @Override
    public VodVo getAuth(String fileName) {
        VodVo vodVo= new VodVo();
        //后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1).toUpperCase();
        String title = fileName.substring(0,fileName.lastIndexOf("."));
        CreateAssetByFileUploadRequest request = new CreateAssetByFileUploadRequest();
        CreateAssetByFileUploadReq body = new CreateAssetByFileUploadReq();
        //转码模板
        body.withTemplateGroupName(hwyunVodConfig.getTemplateGroupName());
        body.withCategoryId(hwyunVodConfig.getCategoryId());
        //视频格式一定是大写的 MP4 .....
        body.withVideoType(suffix);
        body.withVideoName(fileName);
        body.withTitle(title);
        request.withBody(body);
        try {
            CreateAssetByFileUploadResponse response = client.createAssetByFileUpload(request);
            FileAddr fileAddr = response.getTarget();
            //视频媒资ID
            vodVo.setAssetId(response.getAssetId());
            //视频临时桶
            vodVo.setBucket(fileAddr.getBucket());
            //视频长传路径
            vodVo.setObject(fileAddr.getObject());
            //视频region信息
            vodVo.setLocation(fileAddr.getLocation());
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
        }

        return vodVo;
    }

    @Override
    public VodAddressVo getAddress(String videoKey) {
        VodAddressVo vodAddressVo = new VodAddressVo();
        ShowAssetMetaRequest request = new ShowAssetMetaRequest();
        List<String> listRequestAssetId = new ArrayList<>();
        listRequestAssetId.add(videoKey);
        request.withAssetId(listRequestAssetId);
        try {
            ShowAssetMetaResponse response = client.showAssetMeta(request);
            AssetInfo info = response.getAssetInfoArray().get(0);
            List<PlayInfo> list = info.getPlayInfoArray();
            if(!assetInfoStatus.equals(info.getStatus())){
                String coverAddress = "";
                if(info.getBaseInfo() != null && CollectionUtils.isNotEmpty(info.getBaseInfo().getCoverInfoArray())){
                    coverAddress = info.getBaseInfo().getCoverInfoArray().get(0).getCoverUrl();
                }
                if(list == null){
                    vodAddressVo.setAddress("");
                }else if(list.size()>1){
                    vodAddressVo.setAddress(list.get(1).getUrl());
                }else{
                    vodAddressVo.setAddress(list.get(0).getUrl());
                }

                vodAddressVo.setCoverAddress(coverAddress);
                vodAddressVo.setVideoKey(videoKey);
            }
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
        }
        return vodAddressVo;
    }

    @Override
    public List<VodTransProgressVo> getTransProgress(List<String> keys) {
        if(CollectionUtils.isEmpty(keys)){
            return new ArrayList<>();
        }
        List<VodTransProgressVo> vodTransProgressVoList = new ArrayList<>();
        try {
            //华为云视频最多允许查询10个
            int partitionSize = (keys.size() / 10) + 1;
            List<List<String>> partition = ListUtils.partition(keys, partitionSize);

            partition.forEach(keyList -> {
                ListAssetListRequest request = new ListAssetListRequest();
                request.withAssetId(keyList);
                ListAssetListResponse response = client.listAssetList(request);
                if(response!=null && response.getAssets().size()>0){
                    List<AssetSummary> list=response.getAssets();
                    List<VodTransProgressVo> result=new ArrayList<>();
                    for (AssetSummary video:list){
                        VodTransProgressVo vodTransProgressVo=VodTransProgressVo.builder().build();

                        vodTransProgressVo.setVideoKey(video.getAssetId());
                        if(CollectionUtils.isNotEmpty(video.getCovers())){
                            vodTransProgressVo.setVideoCover(video.getCovers().get(0).getCoverUrl());
                        }
                        if(AssetSummary.TranscodeStatusEnum.TRANSCODE_SUCCEED.equals(video.getTranscodeStatus())){
                            //全部转码完成
                            vodTransProgressVo.setTransCode(100L);
                        }else if(AssetSummary.TranscodeStatusEnum.TRANSCODING.equals(video.getTranscodeStatus())){
                            //转码中  多种状态
                            vodTransProgressVo.setTransCode(50L);
                        }else{
                            vodTransProgressVo.setTransCode(0L);
                        }
                        result.add(vodTransProgressVo);
                    }
                    vodTransProgressVoList.addAll(result);
                }
            });
            return vodTransProgressVoList;
        } catch (Exception e) {
            log.info(e.getMessage());
            throw e;
        }
    }

    @Override
    public List<VodTransProgressVo> getTransNotCompleted(){
        //1、查询转码未完成的数据
        List<CmsContentFile> fileList = cmsContentFileMapper.selTransNotCompleted();
        List<VodTransProgressVo> vodTransProgressVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileList)){
            return vodTransProgressVos;
        }
       //2、获取视频key
        List<String> keys = fileList.stream().map(CmsContentFile::getVideoKey).collect(Collectors.toList());
        //3、获取转码进度
        List<VodTransProgressVo> transProgressList = this.getTransProgress(keys);
        //3.1、key->视频key value->封面
        Map<String, String> videoCoverMap = transProgressList.stream().collect(Collectors.toMap(VodTransProgressVo::getVideoKey, VodTransProgressVo::getVideoCover, (a, b) -> a));

        if (CollectionUtils.isNotEmpty(transProgressList)){
            //4.1、批量更新内容附件中的视频的转码进度
            cmsContentFileMapper.batchEditProgress(transProgressList);
            //4.2、批量更新内容中的视频的封面
            List<CmsContentEditCoverUrlDto> coverUrlDtoList = fileList.stream().map(e -> {
                String videoKey = e.getVideoKey();
                String videoCover = videoCoverMap.getOrDefault(videoKey, "");
                return CmsContentEditCoverUrlDto.builder()
                        .id(e.getContentId())
                        .coverImageUrl(videoCover)
                        .build();
            }).collect(Collectors.toList());
            cmsContentMapper.updateCoverUrl(coverUrlDtoList);
        }

        //5、筛选转码未完成的视频
        List<VodTransProgressVo> notCompletedList = transProgressList.stream().filter(vo -> !"100".equals(vo.getTransCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notCompletedList)){
            vodTransProgressVos = notCompletedList.stream().map(e->{
                VodTransProgressVo vodTransProgressVo = BeanUtil.copyProperties(e, VodTransProgressVo.class);
                return vodTransProgressVo;
            }).collect(Collectors.toList());
        }
        return vodTransProgressVos;
    }

    /**
     * 华为云视频点播优化服务
     *
     * @param map
     * @return {@link List}<{@link VodTransProgressVo}>
     */
    @Override
    public List<VodTransProgressVo> getTransNotCompletedV2(Map<String, Object> map) {
        // 华为云视频点播服务的回调
        String videoKey = "";
        String condition = "";

        // 1、查询转码未完成的数据
        List<CmsContentFile> fileList = cmsContentFileMapper.selTransNotCompleted();
        List<VodTransProgressVo> vodTransProgressVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileList)){
            return vodTransProgressVos;
        }

        // 2、获取视频key
        List<String> keys = fileList.stream().map(CmsContentFile::getVideoKey).collect(Collectors.toList());
        // 3、获取转码进度
        List<VodTransProgressVo> transProgressList = this.getTransProgress(keys);
        // 3.1、key -> 视频key value -> 封面
        Map<String, String> videoCoverMap = transProgressList.stream()
                .collect(Collectors.toMap(VodTransProgressVo::getVideoKey, VodTransProgressVo::getVideoCover, (a, b) -> a));
        // 3.2 key -> 视频key value -> id
        Map<String, Long> videoMap = fileList.stream().collect(Collectors.toMap(CmsContentFile::getVideoKey, CmsContentFile::getId, (a, b) -> a));

        // 4、转码成功的回调
        if (transcodeComplete.equals(map.get(subject))) {
            JSONObject messageJson = JSON.parseObject(map.get(message).toString());
            JSONObject transcodeInfoJson = (JSONObject) messageJson.get(transcodeInfo);
            condition = transcodeInfoJson.get(status).toString();
            if (CollectionUtils.isNotEmpty(transProgressList) && transProgressList.size() > NumberUtils.INTEGER_ZERO && "SUCCEED".equals(condition)) {
                videoKey = transcodeInfoJson.get(assetId) == null ? "" : transcodeInfoJson.get(assetId).toString();
                if (StringUtils.isNotEmpty(videoKey)) {
                    // 更新内容附件中的视频转码进度
                    cmsContentFileMapper.updateTransProgressByKey(videoKey, 100L);
                    // 更新内容中的视频封面
                    cmsContentMapper.updateCoverUrlByKey(videoMap.get(videoKey), videoCoverMap.getOrDefault(videoKey, ""));
                }
            }
        } else {
            // 获取订阅确认地址 执行确认订阅
            String url = map.get(subscribeUrl) == null ? "" : map.get(subscribeUrl).toString();
            if (StringUtils.isNotEmpty(url)) {
                try {
                    // 发起HttpClient请求 进行确认订阅
                    new RestTemplate().getForObject(url, String.class);
                } catch (Exception ex) {
                    log.error("确认订阅失败");
                }
            }
        }

        // 5、筛选转码未完成的视频
        List<VodTransProgressVo> notCompletedList = transProgressList.stream().filter(vo -> !"100".equals(vo.getTransCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notCompletedList)) {
            vodTransProgressVos = notCompletedList.stream()
                    .map(e -> BeanUtil.copyProperties(e, VodTransProgressVo.class))
                    .collect(Collectors.toList());
        }
        return vodTransProgressVos;
    }

    @Override
    public HwYunVodConfigVo getConfigInfo() {

        HwYunVodConfigVo hwYunVodConfigVo = HwYunVodConfigVo.builder().build();
        String userName = "";
        try {
            userName = SecurityUtils.getUser().getUsername()+"___"+SecurityUtils.getUser().getPhone();
        } catch (Exception e) {
            log.error(" SecurityUtils.getUser().getUsername()获取用户名失败默认赋值：admin");
            userName = "admin";
        }

        //华为 iam 获取临时凭证
        IamClient clientIam = IamClient.newBuilder()
                .withCredential(auth)
                .withRegion(IamRegion.valueOf(hwyunVodConfig.getRegion()))
                .build();
        CreateTemporaryAccessKeyByTokenRequest request = new CreateTemporaryAccessKeyByTokenRequest();
        CreateTemporaryAccessKeyByTokenRequestBody body = new CreateTemporaryAccessKeyByTokenRequestBody();
        IdentityToken tokenIdentity = new IdentityToken();
        tokenIdentity.withDurationSeconds(36000);
        List<TokenAuthIdentity.MethodsEnum> listIdentityMethods = new ArrayList<>();
        listIdentityMethods.add(TokenAuthIdentity.MethodsEnum.fromValue("token"));
        TokenAuthIdentity identityAuth = new TokenAuthIdentity();
        identityAuth.withMethods(listIdentityMethods)
                .withToken(tokenIdentity);
        TokenAuth authBody = new TokenAuth();
        authBody.withIdentity(identityAuth);
        body.withAuth(authBody);
        request.withBody(body);
        try {
            CreateTemporaryAccessKeyByTokenResponse response = clientIam.createTemporaryAccessKeyByToken(request);
            Credential credential = response.getCredential();
            hwYunVodConfigVo = HwYunVodConfigVo.builder()
                    .accessKeyId(AesUtils.encrypt(credential.getAccess(),userName))
                    .accessKeySecret(AesUtils.encrypt(credential.getSecret(),userName))
                    .securityToken(AesUtils.encrypt(credential.getSecuritytoken(),userName))
                    .templateGroupName(AesUtils.encrypt(hwyunVodConfig.getTemplateGroupName(),userName))
                    .region(AesUtils.encrypt(hwyunVodConfig.getRegion(),userName))
                    .projectId(AesUtils.encrypt(hwyunVodConfig.getProjectId(),userName))
                    .endpoint(AesUtils.encrypt(hwyunVodConfig.getEndpoint(),userName))
                    .userId(AesUtils.encrypt(hwyunVodConfig.getUserId(),userName))
                    .build();
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hwYunVodConfigVo;
    }

    @Override
    public VodConfirmVo confirmVideo(VodConfirmDto vodConfirmDto) {
        VodConfirmVo vodConfirmVo = new VodConfirmVo();
        ConfirmAssetUploadRequest request = new ConfirmAssetUploadRequest();
        ConfirmAssetUploadReq body = new ConfirmAssetUploadReq();
        body.withStatus(ConfirmAssetUploadReq.StatusEnum.fromValue(vodConfirmDto.getStatus()));
        body.withAssetId(vodConfirmDto.getAssetId());
        request.withBody(body);
        try {
            ConfirmAssetUploadResponse response = client.confirmAssetUpload(request);
            vodConfirmVo.setAssetId(response.getAssetId());
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
        }
        return vodConfirmVo;
    }
}
