package com.gok.pboot.portal.vo;

import com.gok.bcp.message.vo.BcpMessageVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 移动端-消息
 *
 * <AUTHOR>
 * @since 2023-03-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageVo extends BcpMessageVO implements Serializable {

    /**
     * 平台logo
     */
    private String logo;

    /**
     * 未读数量
     */
    private Long unReadCount;
}
