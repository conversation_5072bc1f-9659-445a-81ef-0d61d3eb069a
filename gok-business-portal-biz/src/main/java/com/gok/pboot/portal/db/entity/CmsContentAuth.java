package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;

import lombok.Builder;
import lombok.Data;

/**
 * 内容权限表
 *
 * <AUTHOR>
 * @TableName cms_content_auth
 * @date 2024/01/12
 */
@TableName(value ="cms_content_auth")
@Data
@Builder
public class CmsContentAuth implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDate createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 部门id
     */
    private Long deptId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}