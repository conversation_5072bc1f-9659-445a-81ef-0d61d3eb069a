package com.gok.pboot.portal.dto;


import lombok.Data;

/**
 * 工单确认保存 DTO
 *
 * <AUTHOR>
 * @date 2024/01/22
 * @since 2023-08-28
 */
@Data
public class WorkOrderConfirmSaveDto {

    /**
     * 反馈ID
     */
    private Long feedbackId;

    /**
     * 反馈人
     */
    private String createBy;

    /**
     * 反馈人ID
     */
    private Long feedbackUserId;

    /**
     * 指派处理人员ID
     */
    private Long handleWorkerId;

    /**
     * 指派处理人员姓名
     */
    private String handleWorker;

    /**
     * 跟进人员ID
     */
    private Long followedWorkerId;

    /**
     * 跟进人员姓名
     */
    private String followedWorker;

    /**
     * 紧急程度（0一般，1紧急，2紧急，3特别紧急）
     */
    private Integer urgencyDegree;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 反馈类型（0:优化建议、1：功能异常、2:其他）
     */
    private Integer type;

    /**
     * 批转操作内容
     */
    private String operationContent;

}
