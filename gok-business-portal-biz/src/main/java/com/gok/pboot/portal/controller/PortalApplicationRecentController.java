package com.gok.pboot.portal.controller;

import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalApplicationRecent;
import com.gok.pboot.portal.dto.PortalApplicationRecentDto;
import com.gok.pboot.portal.service.PortalApplicationRecentService;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.PortalApplicationRecentVo;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门户最近应用程序控制器
 *
 * <AUTHOR>
 * @date 25/10/2023
 * @menu 门户最近应用
 */
@RestController
@Api(tags = "门户最近应用")
@RequestMapping("portalApplicationRecent")
public class PortalApplicationRecentController {
    @Resource
    private PortalApplicationRecentService service;

    /**
     * 保存最近应用
     *
     * @param req 要求
     * @return {@link R}
     */
    @PostMapping("saveRecent")
    public R saveRecent(@RequestBody PortalApplicationRecentDto req){
        return service.saveRecent(req);
    }

    /**
     * 最近应用列表
     *
     * @param userId 用户标识
     * @return {@link R}<{@link List}<{@link ApplicationIconVo}>>
     */
    @GetMapping("list/{userId}")
    public R<List<ApplicationIconVo>> list(@PathVariable Long userId){
        return service.recentList(userId);
    }

}
