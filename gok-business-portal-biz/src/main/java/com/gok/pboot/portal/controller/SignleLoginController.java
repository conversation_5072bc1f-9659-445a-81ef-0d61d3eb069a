package com.gok.pboot.portal.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.bcp.upms.vo.SysOauthClientDetailsVO;
import com.gok.pboot.portal.common.DBApi;
import com.gok.pboot.portal.enums.UserStatusEnum;
import com.gok.pboot.portal.util.PackageUrlUtils;
import com.gok.pboot.portal.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 单点登录
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Slf4j
@RestController
@RequestMapping("/signleLogin")
@RequiredArgsConstructor
@Api(tags = "单点登录")
public class SignleLoginController {

    private final PackageUrlUtils packageUrlUtils;

    private final RemoteOutService remoteOutService;

    private final DBApi dbApi;

    /**
     * 单点登录跳转
     *
     * @param url 原路径
     * @param appId 应用id
     * @return {@link R}
     */
    @GetMapping
    @ApiOperation(value = "登录跳转", notes = "登录跳转")
    public R login(@RequestParam("url") String url, @RequestParam("appId") Long appId) {
        // 1、判断当前用户是否为空
        PigxUser user = UserUtils.getUser();
        if (!Optional.ofNullable(user).isPresent()) {
            log.info("用户不存在，获取跳转token失败");
            return R.failed();
        }
        // 2、判断路径是否为空
        if (StrUtil.isEmpty(url)) {
            log.info("路径为空，获取跳转token失败");
            return R.failed();
        }
        // 2.1、路径判断组装
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        // 2、根据登录用户id与应用id获取应用列表
        List<SysOauthClientDetailsVO> oauthClientDetailsVOList
                = remoteOutService.getAppListByUserId(user.getId(), NumberUtils.LONG_MINUS_ONE).getData();
        log.info("应用列表:{}", oauthClientDetailsVOList);
        // 2.1、无应用列表直接返回
        if (CollUtil.isEmpty(oauthClientDetailsVOList) && oauthClientDetailsVOList.isEmpty()) {
            return R.failed();
        }
        // 2.2、应用列表的id不包括applicationId直接返回
        List<SysOauthClientDetailsVO> idList = oauthClientDetailsVOList.stream()
                .filter(o -> o.getId().equals(appId))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(idList) && idList.isEmpty()) {
            return R.failed();
        }
        // 3、拼接地址
        // 3.1、校验OA系统是否存在该用户
        List<UserVo> userVoList = dbApi.getOAUserId(user.getPhone(), Arrays.asList(UserStatusEnum.valid));
        if (CollUtil.isEmpty(userVoList) && userVoList.isEmpty()) {
            return R.failed();
        }
        // 3.2、返回地址
        String isUrl = packageUrlUtils.getUrl(url, appId, userVoList.get(NumberUtils.INTEGER_ZERO).getLoginId());
        return StrUtil.EMPTY.equals(isUrl) ? R.failed() : R.ok(isUrl);
    }
}
