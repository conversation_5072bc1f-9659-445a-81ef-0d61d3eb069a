package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.db.entity.ProjectFeedbackLog;
import com.gok.pboot.portal.vo.ProjectFeedbackLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import java.util.List;

/**
 * <P>
 * 针对表针对表【project_feedback_log(反馈批转信息表)】的数据库操作Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Mapper
public interface ProjectFeedbackLogMapper extends BaseMapper<ProjectFeedbackLog> {

    /**
     * 查询项目反馈批转信息根据反馈id
     *
     * @param feedbackID 反馈id
     * @return {@link List}<{@link ProjectFeedbackLog}>
     */
    List<ProjectFeedbackLog> queryProjectFeedbackLogByFeedbackID(Long feedbackID);

    /**
     * 通过FeedbackId查询反馈批转信息记录
     *
     * @param feedbackId 反馈Id
     * @return 记录
     */
    List<ProjectFeedbackLogVo> queryAllByFeedbackId(@Param("feedbackId") Long feedbackId);

}




