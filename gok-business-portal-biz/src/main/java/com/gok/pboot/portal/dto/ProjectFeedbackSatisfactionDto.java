package com.gok.pboot.portal.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import com.gok.pboot.common.core.validation.annotation.StringVerify;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 项目反馈满意度dto
 *
 * <AUTHOR>
 * @date 28/8/2023
 */
@Data
public class ProjectFeedbackSatisfactionDto {

    /**
     * ID
     */
    @NotNull
    private Long id;

    /**
     * 服务态度
     */
    @NotNull
    @IntegerVerify(name = "服务态度",max = 5,min = 0)
    private Integer serviceAttitude;

    /**
     * 处理效果
     */
    @NotNull
    @IntegerVerify(name = "处理效果",max = 5,min = 0)
    private Integer serviceEffectiveness;

    /**
     * 处理效率
     */
    @NotNull
    @IntegerVerify(name = "处理效率",max = 5,min = 0)
    private Integer serviceEfficiency;

    /**
     * 服务感受
     */
    @NotNull
    @StringVerify(name = "服务感受",maxLen = 300)
    private String serviceFeel;
}
