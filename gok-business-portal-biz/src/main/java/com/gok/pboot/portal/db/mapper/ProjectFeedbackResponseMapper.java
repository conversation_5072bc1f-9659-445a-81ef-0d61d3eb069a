package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.db.entity.ProjectFeedbackResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;


/**
 * <P>
 * 针对表【project_feedback_response(反馈回复表)】的数据库操作Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Mapper
public interface ProjectFeedbackResponseMapper extends BaseMapper<ProjectFeedbackResponse> {

    /**
     * 根据feedbackID查询
     *
     * @param feedbackId 反馈Id
     * @return 回复内容
     */
    ProjectFeedbackResponse getOneByFeedbackId(@Param("feedbackId") Long feedbackId);

    ProjectFeedbackResponse queryProjectFeedbackResponseByFeedbackID(Long id);

    /**
     * 修改回复
     *
     * @param feedbackId      反馈id
     * @param responseContent 回复内容
     * @param fileIds         回复文件ids
     * @param userName        创建人名称
     * @return R
     */
    boolean updateResponse(@Param("feedbackId") Long feedbackId,
                           @Param("content") String responseContent,
                           @Param("fileIds") String fileIds,
                           @Param("updateBy") String userName);
}




