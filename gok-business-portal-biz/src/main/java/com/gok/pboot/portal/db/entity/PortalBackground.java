package com.gok.pboot.portal.db.entity;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.dto.PortalBackgroundSaveOrUpdateDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 门户背景管理
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("portal_background")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "门户背景管理")
public class PortalBackground extends Model<PortalBackground> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 适用日期开始时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "适用日期开始时间")
    private LocalDateTime startTime;

    /**
     * 适用日期结束时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "适用日期结束时间")
    private LocalDateTime endTime;

    /**
     * 个人生日适用类型（0不适用，1适用）
     * {@link com.gok.pboot.portal.enums.SuitTypeEnum}
     */
    @ApiModelProperty(value = "个人生日适用类型")
    private Integer birthday;

    /**
     * 入职周年适用类型（0不适用，1适用）
     * {@link com.gok.pboot.portal.enums.SuitTypeEnum}
     */
    @ApiModelProperty(value = "入职周年适用类型")
    private Integer anniversary;

    /**
     * 上架状态（0已上架、1未上架）
     * {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     */
    @ApiModelProperty(value = "上架状态（0已上架、1未上架）")
    private Integer status;

    /**
     * 门户背景文件id
     */
    @ApiModelProperty(value = "门户背景文件id")
    private Long portalFileId;

    /**
     * 个人背景文件id
     */
    @ApiModelProperty(value = "个人背景文件id")
    private Long personFileId;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 默认标志位
     */
    @ApiModelProperty(value = "默认标志位")
    private Integer defaultFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建者名称
     */
    @ApiModelProperty(value = "创建者名称")
    private String createUserName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * delFlag
     */
    @TableLogic
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

    /**
     * 应用背景文件id
     */
    @ApiModelProperty(value = "应用背景文件id")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private Long appFileId;

    public static PortalBackground buildSave(PortalBackgroundSaveOrUpdateDto request) {
        PortalBackground portalBackground = BeanUtil.copyProperties(request, PortalBackground.class);
        portalBackground.setCreateUserName(SecurityUtils.getUser().getName());
        return portalBackground;
    }

    public static PortalBackground buildUpdate(PortalBackgroundSaveOrUpdateDto request) {
        PortalBackground portalBackground = BeanUtil.copyProperties(request, PortalBackground.class);
        return portalBackground;
    }

}
