package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("portal_application_manage")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "应用管理")
public class PortalApplicationManage extends Model<PortalApplicationManage> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;


    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String applicationName;

    /**
     * 应用分类id
     */
    @ApiModelProperty(value = "应用分类id")
    private Long categoryId;

    /**
     * 所属应用id
     */
    @ApiModelProperty(value = "所属应用id")
    private Long belongApplicationId;

    /**
     * 所属应用名称
     */
    @ApiModelProperty(value = "所属应用名称")
    private String belongApplicationName;

    /**
     * 链接地址
     */
    @ApiModelProperty(value = "链接地址")
    private String linkAddress;

    /**
     * 使用文档地址
     */
    @ApiModelProperty(value = "使用文档地址")
    private String documentAddress;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Long fileId;

    /**
     * 上架状态（0已上架、1未上架）
     * {@link com.gok.pboot.portal.enums.LaunchStatusEnum}
     */
    @ApiModelProperty(value = "上架状态（0已上架、1未上架）")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    @TableField(fill = FieldFill.INSERT_UPDATE,updateStrategy = FieldStrategy.NOT_EMPTY)
    private LocalDateTime lunchTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * delFlag
     */
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

}
