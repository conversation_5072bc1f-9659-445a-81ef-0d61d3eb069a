package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.service.ICmsOperationLogService;
import com.gok.pboot.portal.vo.CmsOperationLogVo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 内容操作记录表 前端控制器
 *
 * <AUTHOR>
 * @description 内容操作记录表 前端控制器
 * @menu 内容操作记录
 * @date 2023/08/01
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/cmsOperationLog")
public class CmsOperationLogController {

    private final ICmsOperationLogService service;

    /**
     * 通过内容id分页查询内容操作记录列表
     *
     * @param contentId 内容id
     * @param page      分页参数
     * @return {@link R}<{@link Page}<{@link CmsOperationLogVo}>>
     */
    @GetMapping("/findPage/{contentId}")
    @ApiOperation(value = "通过内容id查询内容操作记录列表", notes = "通过内容id查询内容操作记录列表")
    public R<Page<CmsOperationLogVo>> findPageByContentId(@PathVariable(value = "contentId") Long contentId, Page page) {
        return R.ok(service.findPageByContentId(contentId, page));
    }

}
