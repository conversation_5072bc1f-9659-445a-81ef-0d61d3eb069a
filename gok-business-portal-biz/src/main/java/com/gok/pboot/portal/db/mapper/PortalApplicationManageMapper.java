package com.gok.pboot.portal.db.mapper;

import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.PortalApplicationManage;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.PortalApplicationManageBoxVo;
import com.gok.pboot.portal.vo.SourceFileUrlVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * 应用管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Mapper
public interface PortalApplicationManageMapper extends BaseMapper<PortalApplicationManage> {

    /**
     * 通过id列表查询实体
     * @param applicationIds id列表
     * @return vo列表
     */
    List<ApplicationIconVo> queryVosByIds(@Param("ids") List<Long> applicationIds);

    /**
     * 新增并返回id
     * @param applicationManage
     * @return
     */
    Long insertReturnId(PortalApplicationManage applicationManage);

    /**
     * 通过ids寻找该用户拥有权限的应用（取六个）
     * @param applicationIds ids
     * @return 应用列表
     */
    List<ApplicationIconVo>queryRightApplicationByIds(@Param("applicationIds") List<Long> applicationIds);


    /**
     * 模糊查询拥有权限的应用
     * @param applicationIds 拥有权限应用ids
     * @param applicationName 模糊查询信息
     * @return 应用列表
     */
    List<ApplicationIconVo>queryRightApplicationByLike(@Param("applicationIds") List<Long> applicationIds
            ,@Param("applicationName") String applicationName);


    /**
     * 获取未上架的应用
     * @return 应用列表
     */
    List<PortalApplicationManageBoxVo> queryOffShelf();


    /**
     * 获取应用来源枚举与文件logo
     *
     * @return {@link List}<{@link SourceFileUrlVo}>
     */
    List<SourceFileUrlVo> getFileUrl();

}
