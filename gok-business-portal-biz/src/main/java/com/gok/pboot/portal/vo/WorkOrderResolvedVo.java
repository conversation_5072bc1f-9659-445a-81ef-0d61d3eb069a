package com.gok.pboot.portal.vo;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 已解决工单 VO
 *
 * <AUTHOR>
 * @date 2024/01/22
 * @since 2023-08-28
 */
@Data
public class WorkOrderResolvedVo {

    /**
     * 回复内容
     */
    private String responseContent;

    /**
     * 回复相关图片
     */
    private List<String> responseFileIds;

    /**
     * 指派处理人Id
     */
    private Long handleWorkerId;

    /**
     * 指派处理人员
     */
    private String handleWorker;

    /**
     * 跟进人Id
     */
    private Long followedWorkerId;

    /**
     * 跟进人
     */
    private String followedWorker;

    /**
     * 紧急程度
     */
    private Integer urgencyDegree;

    /**
     * 紧急程度中文
     */
    private String urgencyDegreeStr;

    /**
     * 反馈人
     */
    private String createBy;

    /**
     * 反馈人ID
     */
    private Long feedbackUserId;

    /**
     * 部门
     */
    private String dept;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈类型中文
     */
    private String typeStr;

    /**
     * 反馈时间
     */
    private LocalDateTime createTime;

    /**
     * 涉及系统
     */
    private String applicationName;

    /**
     * 反馈相关图片
     */
    private List<String> feedbackFileIds;

    /**
     * 批转信息
     */
    private List<ProjectFeedbackLogVo> logVos;
}
