package com.gok.pboot.portal.controller;

import com.alibaba.fastjson.JSONObject;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.ChatMessageDto;
import com.gok.pboot.portal.dto.ContentDto;
import com.gok.pboot.portal.dto.ListConversationsDto;
import com.gok.pboot.portal.dto.ListMessagesDto;
import com.gok.pboot.portal.service.IChatMessageService;
import com.gok.pboot.portal.vo.CompletionsVo;
import com.gok.pboot.portal.vo.HistoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 *
 * 客服助手机器人
 * <AUTHOR>
 * @description 客服助手机器人
 * @menu 国科机器人
 * @since 2023-06-08
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/chat")
@Api(tags = "客服聊天机器人")
public class ChatMessageController {

    private final IChatMessageService iChatMessageService;

    /**
     * fastGpt新客服助手首次提示语
     *
     * @return {@link R}<{@link Map}>
     */
    @GetMapping("/firstTip")
    @ApiOperation(value = "新客服助手首次提示语", notes = "新客服助手首次提示语")
    public Map<String, Object> fastFirstTip() {
        return iChatMessageService.fastFirstTip();
    }

    /**
     * fastGpt新客服助手聊天
     *
     * @param contentDto {@link ContentDto}
     * @return {@link R}<{@link CompletionsVo}>
     */
    @PostMapping("/fastTalk")
    @ApiOperation(value = "新客服助手聊天", notes = "新客服助手聊天")
    public R<CompletionsVo> talk(@RequestBody ContentDto contentDto) {
        return iChatMessageService.talk(contentDto.getContent());
    }

    /**
     * fastGpt获取聊天列表
     *
     * @param page 当前页
     * @param limit 每页条数
     * @param currentSize 当前页条数
     * @return {@link R}<{@link List}<{@link HistoryVo}>>
     */
    @GetMapping("/getHistoryList")
    @ApiOperation(value = "获取聊天列表", notes = "获取聊天列表")
    public R<List<HistoryVo>> getHistoryList(@RequestParam("page") Long page,
                                             @RequestParam("limit") Long limit,
                                             @RequestParam("currentSize") Integer currentSize) {
        return R.ok(iChatMessageService.getHistoryList(page, limit, currentSize));
    }

    /**
     * dify首次提示语
     *
     * @return {@link R}<{@link Map}>
     */
    @GetMapping("/difyPrologue")
    @ApiOperation(value = "首次提示语", notes = "首次提示语")
    public R<Map<String, Object>> firstTip() {
        return R.ok(iChatMessageService.firstTip());
    }

    /**
     * dify创建会话信息或基于此前的对话继续发送消息
     *
     * @param contentDto 输入聊天内容
     * @return {@link R}<{@link ChatMessageDto}>
     */
    @PostMapping("/difyTalk")
    @ApiOperation(value = "创建会话信息", notes = "创建会话信息")
    public R<Object> chatMessages(@RequestBody ContentDto contentDto) {
        return iChatMessageService.chatMessages(contentDto.getContent());
    }

    /**
     * dify小助手聊天记录
     *
     * @param firstId 第一条聊天记录id
     * @param limit 条数
     * @return {@link R}<{@link ListMessagesDto}>
     */
    @GetMapping("/difyMessages")
    @ApiOperation(value = "获取聊天列表", notes = "获取聊天列表")
    public R<Object> listMessages(@RequestParam(value = "firstId", required = false) String firstId,
                                  @RequestParam(value = "limit", required = false) Integer limit) {
        return iChatMessageService.listMessages(firstId, limit);
    }


    /**
     * dify 删除掉rds的缓存key
     *
     * @param userId
     * @return
     */
    @DeleteMapping("delete/{userId}")
    @ApiOperation(value = "dify 删除掉rds的缓存key",notes = "dify 删除掉rds的缓存key")
    public R delRdsKey(@PathVariable String userId){
        return iChatMessageService.delRdsKey(userId);
    }

    /**
     * dify聊天会话列表（已弃用）
     *
     * @param lastId 最后一次聊天id
     * @param limit 条数
     * @return {@link R}<{@link ListConversationsDto}>
     * @deprecated
     */
    @Deprecated
    @GetMapping("/listConversations")
    @ApiOperation(value = "获取聊天会话列表", notes = "获取聊天会话列表")
    public R<Object> listConversations(@RequestParam(value = "lastId", required = false) String lastId,
                                       @RequestParam("limit") Integer limit) {
        return iChatMessageService.listConversations(lastId, limit);
    }




}
