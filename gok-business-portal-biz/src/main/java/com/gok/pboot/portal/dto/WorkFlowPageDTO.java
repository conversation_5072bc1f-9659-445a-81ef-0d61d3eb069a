package com.gok.pboot.portal.dto;

import com.gok.bcp.flowable.task.common.enums.flow.FlowStatusEnum;
import com.gok.pboot.portal.enums.WorkFlowTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 工作流程分页参数
 *
 * <AUTHOR>
 * @since 2023-03-19
 */
@Data
public class WorkFlowPageDTO implements Serializable {

    /**
     * 自动计数
     */
    private boolean countable = true;

    /**
     * 平台类型
     */
    private String source;

    /**
     * 类型 {@link com.gok.pboot.portal.enums.WorkFlowTypeEnum}
     */
    @NotNull(message = "任务/流程类型不能为空")
    private WorkFlowTypeEnum workflowTaskType;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long pageNo;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long pageSize;

    /**
     * 任务状态列表
     */
    private List<String> statusList;

    /**
     * 代办状态 {@link com.gok.bcp.flowable.task.common.enums.flow.FlowStatusEnum}
     */
    private FlowStatusEnum flowStatus;
}
