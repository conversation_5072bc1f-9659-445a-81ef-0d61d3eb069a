package com.gok.pboot.portal.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalApplicationCategory;
import com.gok.pboot.portal.dto.PortalApplicationCategoryDto;
import com.gok.pboot.portal.dto.PortalApplicationCategoryDtoItem;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.PortalApplicationCategoryVo;

import java.util.List;

/**
 * <p>
 * 应用分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IPortalApplicationCategoryService extends IService<PortalApplicationCategory> {


    /**
     * 获取全部的应用分类
     *
     * @return 分类list
     */
    List<PortalApplicationCategoryVo> getAllCategory();

    /**
     * 新增，删除，修改的接口
     *
     * @param dto
     * @return
     */
    R editList(PortalApplicationCategoryDto dto);


}
