package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.PortalBackgroundDto;
import com.gok.pboot.portal.dto.PortalBackgroundSaveOrUpdateDto;
import com.gok.pboot.portal.service.PortalBackgroundService;
import com.gok.pboot.portal.vo.PortalBackgroundDetailVo;
import com.gok.pboot.portal.vo.PortalBackgroundVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 门户背景管理表 前端控制器
 * @description 门户背景管理表 前端控制器
 * @menu 门户背景管理
 * <AUTHOR>
 * @date 2023/08/03
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/portalBackground")
@Api(tags = "门户背景管理表")
public class PortalBackgroundController {

    private final PortalBackgroundService service;

    /**
     * 分页查询背景列表
     *
     * @param page    分页参数
     * @param request 查询请求
     * @return {@link R}<{@link Page}<{@link PortalBackgroundVo}>>
     */
    @GetMapping("/findPage")
    @ApiOperation(value = "分页查询背景列表", notes = "分页查询背景列表")
    public R<Page<PortalBackgroundVo>> findPage(Page page, PortalBackgroundDto request) {
        return R.ok(service.findPage(page, request));
    }

    /**
     * 查询背景详情
     *
     * @param backgroundId 背景id
     * @return {@link R}<{@link PortalBackgroundDetailVo}>
     */
    @GetMapping("/findDetail/{backgroundId}")
    @ApiOperation(value = "查询背景详情", notes = "查询背景详情")
    public R<PortalBackgroundDetailVo> findDetail(@PathVariable Long backgroundId) {
        return R.ok(service.findDetail(backgroundId));
    }

    /**
     * 查询用户适配背景
     *
     * @return {@link R}<{@link PortalBackgroundVo}>
     */
    @GetMapping
    @ApiOperation(value = "查询用户适配背景", notes = "查询用户适配背景")
    public R<PortalBackgroundVo> find() {
        return service.find();
    }

    /**
     * 新增门户背景
     *
     * @param request 新增请求
     * @return {@link R}<{@link Long}>
     */
    @PostMapping
    @ApiOperation(value = "新增门户背景", notes = "新增门户背景")
    public R<Long> save(@Valid @RequestBody PortalBackgroundSaveOrUpdateDto request) {
        return R.ok(service.save(request));
    }

    /**
     * 更新门户背景
     *
     * @param request 更新请求
     * @return {@link R}<{@link Long}>
     */
    @PutMapping
    @ApiOperation(value = "更新门户背景", notes = "更新门户背景")
    public R<Long> update(@Valid @RequestBody PortalBackgroundSaveOrUpdateDto request) {
        return R.ok(service.update(request));
    }

    /**
     * 上下架门户背景
     *
     * @param backgroundId     id
     * @param status 状态
     * @return {@link R}<{@link Long}>
     */
    @PutMapping("/status/{backgroundId}")
    @ApiOperation(value = "上下架门户背景", notes = "上下架门户背景")
    public R<Long> updateStatus(@PathVariable("backgroundId") Long backgroundId, @RequestParam Integer status) {
        return R.ok(service.updateStatus(backgroundId, status));
    }

    /**
     * 批量删除门户背景
     *
     * @param ids 门户背景主键id集合[]
     * @return {@link R}<{@link Long}>
     */
    @DeleteMapping
    @ApiOperation(value = "批量删除门户背景", notes = "批量删除门户背景")
    public R<List<Long>> batchDelete(@RequestBody List<Long> ids) {
        return R.ok(service.batchDelete(ids));
    }

}
