package com.gok.pboot.portal.service;

import com.gok.components.common.util.R;
import com.gok.pboot.portal.db.entity.PortalApplicationRecent;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.portal.dto.PortalApplicationRecentDto;
import com.gok.pboot.portal.vo.ApplicationIconVo;
import com.gok.pboot.portal.vo.PortalApplicationRecentVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【portal_application_recent(应用中心-最近应用)】的数据库操作Service
* @createDate 2023-10-25 11:22:48
*/
public interface PortalApplicationRecentService extends IService<PortalApplicationRecent> {

    /**
     * 保存最近应用
     *
     * @param req 要求
     * @return {@link R}
     */
    R saveRecent(PortalApplicationRecentDto req);

    /**
     * 最近应用列表
     *
     * @param userId 用户标识
     * @return {@link R}<{@link List}<{@link ApplicationIconVo}>>
     */
    R<List<ApplicationIconVo>> recentList(Long userId);
}
