package com.gok.pboot.portal.common.client;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

import java.util.List;

/**
 * forest 请求第三方接口
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
public interface ForestClient {

    /**
     * 获取DBApi token
     *
     * @param url 路径
     * @param appId appId
     * @param secret 密钥
     * @return token
     */
    @Get("${url}/token/generate?appid=${appId}&secret=${secret}")
    JSONObject getDbApiToken(@Var("url") String url, @Var("appId") String appId, @Var("secret") String secret);

    /**
     * 获取员工id
     *
     * @param url 路口
     * @param token token
     * @param mobile 手机号
     * @param statusList 状态
     * @return loginId 员工登录id
     */
    @Post(url = "${url}/api/getOAUserId",headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getOAUserId(@Var("url") String url, @Var("token") String token,
                           @Body("mobile") String mobile, @Body("status") List<Integer> statusList);

    /**
     * 根据中台id获取人员id
     *
     * @param url 路口
     * @param token token
     * @param userId 中台id
     * @return loginId 员工登录id
     */
    @Post(url = "${url}/api/getUserIdByZenTao",headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getUserIdByZenTao(@Var("url") String url, @Var("token") String token,
                                 @Body("userId") Long userId);
}
