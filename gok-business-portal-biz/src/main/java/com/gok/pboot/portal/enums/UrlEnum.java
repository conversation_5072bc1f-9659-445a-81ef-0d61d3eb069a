package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 拼接地址 Enum
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Getter
public enum UrlEnum implements ValueEnum<String> {

    /**
     * 河狸学途教学端
     */
    IOIE_EDU("edu", "河狸学途"),

    /**
     * 河狸学途租户端
     */
    IOIE_TENANT("tenant", "河狸学途-租户端"),

    /**
     * 河狸学途运管端
     */
    IOIE_OPERATION("operation", "河狸学途-运管端"),

    /**
     * 禅道
     */
    ZENTAO("zentao", "禅道"),

    /**
     * OA系统
     */
    OA("OA", "OA系统");

    private String value;

    private String name;

    UrlEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
