package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.CmsContentFileUploadDto;
import com.gok.pboot.portal.service.ICmsContentFileService;
import com.gok.pboot.portal.vo.CmsContentFileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 内容附件表 前端控制器
 *
 * <AUTHOR>
 * @description 内容附件表 前端控制器
 * @menu 内容附件
 * @since 2023-08-03
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/cmsContentFile")
@Api(tags = "内容附件表")
public class CmsContentFileController {

    private final ICmsContentFileService service;

    /**
     * 分页查询内容附件
     *
     * @param page      分页请求
     * @param contentId 内容id
     * @return {@link R}<{@link Page}<{@link CmsContentFileVo}>>
     */
    @GetMapping("/findPage/{contentId}")
    @ApiOperation(value = "分页查询内容附件", notes = "分页查询内容附件")
    public R<Page<CmsContentFileVo>> findPage(Page page, @PathVariable("contentId") Long contentId) {
        return R.ok(service.findPage(page, contentId), "操作成功");
    }

    /**
     * 批量上传附件
     *
     * @param request 附件上传请求
     * @return {@link R}
     */
    @PostMapping()
    @ApiOperation(value = "批量上传附件", notes = "批量上传附件")
    public R batchUpload(@Validated @RequestBody CmsContentFileUploadDto request) {
        return R.ok(service.batchUpload(request), "操作成功");
    }

}
