package com.gok.pboot.portal.enums;

import lombok.Getter;

/**
 * 反馈紧急的程度
 *
 * <AUTHOR>
 * @since 2023-08-24
 */

@Getter
public enum FeedbackUrgencyDegreeEnum {

    /**
     * 4 一般
     */
    IV(4, "一般"),

    /**
     * 3 较紧急
     */
    III(3, "较紧急"),

    /**
     * 2 紧急
     */
    II(2, "紧急"),

    /**
     * 1 特别紧急
     */
    I(1, "特别紧急");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FeedbackUrgencyDegreeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按值获取名称
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String getNameByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (FeedbackUrgencyDegreeEnum feedbackUrgencyDegreeEnum : FeedbackUrgencyDegreeEnum.values()) {
            if (feedbackUrgencyDegreeEnum.getValue().equals(value)) {
                return feedbackUrgencyDegreeEnum.getName();
            }
        }
        return null;
    }
}
