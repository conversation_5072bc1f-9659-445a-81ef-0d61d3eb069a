package com.gok.pboot.portal.vo;

import com.gok.bcp.flowable.task.vo.TaskStatusCountVo;
import com.gok.bcp.flowable.task.vo.WorkflowStatusCountVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 任务/流程个数
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowTaskTotal implements Serializable {

    /**
     * 任务状态集合
     */
    List<TaskStatusCountVo> taskStatusCountVo;

    /**
     * 流程状态集合
     */
    List<WorkflowStatusCountVo> workflowCountVos;

    /**
     * 任务状态个数
     */
    Long taskTotal;

    /**
     * 流程状态个数
     */
    Long workflowTotal;
}
