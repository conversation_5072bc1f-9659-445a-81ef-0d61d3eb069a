package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 反馈回复表
 * @TableName project_feedback_response
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("project_feedback_response")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "反馈批转信息表")
public class ProjectFeedbackResponse extends Model<ProjectFeedbackResponse> {
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 反馈id
     */
    @ApiModelProperty(value = "反馈id")
    private Long feedbackId;

    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容")
    private String responseContent;

    /**
     * 文件ids，多个文件用,隔开
     */
    @ApiModelProperty(value = "文件ids，多个文件用,隔开")
    private String fileIds;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @ApiModelProperty(value = "逻辑删除")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;


}