package com.gok.pboot.portal.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.portal.db.entity.CmsOperationLog;
import com.gok.pboot.portal.db.entity.PortalHrmResource;
import com.gok.pboot.portal.db.mapper.CmsOperationLogMapper;
import com.gok.pboot.portal.dto.CmsOperationLogSaveDto;
import com.gok.pboot.portal.enums.OperationTypeEnum;
import com.gok.pboot.portal.service.ICmsOperationLogService;
import com.gok.pboot.portal.service.IHomePageService;
import com.gok.pboot.portal.vo.CmsOperationLogVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * CMS操作日志服务实现
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CmsOperationLogServiceImpl extends ServiceImpl<CmsOperationLogMapper, CmsOperationLog> implements ICmsOperationLogService {

    private final IHomePageService homePageService;

    @Override
    public Page<CmsOperationLogVo> findPageByContentId(Long contentId, Page page) {
        // 分页查询
        Page<CmsOperationLog> pageRes = baseMapper.findPage(page, contentId);
        Page<CmsOperationLogVo> voPage = new Page<>();

        if (CollUtil.isEmpty(pageRes.getRecords())) {
            return voPage;
        }

        List<CmsOperationLogVo> voList = new ArrayList<>();
        pageRes.getRecords().forEach(r -> voList.add(CmsOperationLogVo.buildVo(r)));

        voPage.setTotal(voList.size());
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long syncCmsOperationLog(Long contentId, String title, OperationTypeEnum operationTypeEnum) {
        if (!Optional.ofNullable(contentId).isPresent()) {
            log.warn("内容id字段为空，同步操作日志操作结束！");
            return null;
        }

        // 根据花名册信息获取用户信息
        PortalHrmResource portalHrmResource =
                Optional.ofNullable(homePageService.getCurrentUser()).orElse(new PortalHrmResource());

        CmsOperationLogSaveDto cmsOperationLogSaveDto = new CmsOperationLogSaveDto();
        cmsOperationLogSaveDto.buildDto(portalHrmResource, operationTypeEnum, contentId, title);
        CmsOperationLog entity = CmsOperationLog.buildSave(cmsOperationLogSaveDto);
        baseMapper.insert(entity);
        return entity.getId();
    }

}
