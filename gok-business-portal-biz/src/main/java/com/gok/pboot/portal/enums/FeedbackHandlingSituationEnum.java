package com.gok.pboot.portal.enums;

import lombok.Getter;

/**
 * 反馈处理情况枚举
 *
 * <AUTHOR>
 * @date 29/8/2023
 */

@Getter
public enum FeedbackHandlingSituationEnum {
    /**
     * 0 待处理
     */
    CONFIRM(0, "待处理"),

    /**
     * 1 处理中
     */
    PROCESSING(1, "处理中"),
    /**
     * 2 取消
     */
    CANCEL(2, "取消"),

    /**
     * 3 搁置
     */
    SHELVE(3, "搁置"),

    /**
     * 4 已处理
     */
    PROCESSED(4, "已处理"),

    /**
     * 5 已解决
     */
    RESOLVED(5, "已解决"),


    /**
     * 6 已关闭
     */
    CLOSED(6, "已关闭");


    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FeedbackHandlingSituationEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 按值获取名称
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String getNameByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (FeedbackHandlingSituationEnum feedbackHandlingSituationEnum : FeedbackHandlingSituationEnum.values()) {
            if (feedbackHandlingSituationEnum.getValue().equals(value)) {
                return feedbackHandlingSituationEnum.getName();
            }
        }
        return null;
    }
}
