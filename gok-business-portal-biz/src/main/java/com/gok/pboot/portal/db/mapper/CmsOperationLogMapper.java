package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.portal.db.entity.CmsOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 内容操作记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Mapper
public interface CmsOperationLogMapper extends BaseMapper<CmsOperationLog> {

    /**
     * 分页查询数据库数据
     *
     * @param page      分页参数
     * @param contentId 内容id
     * @return {@link Page}<{@link CmsOperationLog}>
     */
    Page<CmsOperationLog> findPage(Page page, @Param("contentId") Long contentId);

    /**
     * 根据对象类集合批量插入
     *
     * @param entityList 实体列表
     */
    void batchSave(List<CmsOperationLog> entityList);

    /**
     * 根据内容id集合进行逻辑删除
     *
     * @param idList 内容id集合[]
     * @return int
     */
    int deleteByIdList(@Param("idList") List<Long> idList);

}
