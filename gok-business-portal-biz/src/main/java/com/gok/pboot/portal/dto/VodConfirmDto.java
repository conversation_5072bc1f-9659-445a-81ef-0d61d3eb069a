package com.gok.pboot.portal.dto;

import com.gok.pboot.common.core.validation.annotation.StringVerify;

import lombok.Data;


/**
 * @Description   :  确认媒资
 * <AUTHOR>  luoyq
 * @date          :  2023/8/14 10:35
 */
@Data
public class VodConfirmDto {

    /**
     * 视频key
     */
    @StringVerify(name = "视频key",required = true)
    private String assetId;


    /**
     * status
     * CREATED：创建成功。
     * FAILED：创建失败。
     * CANCELLED：创建取消。
     *
     */
    @StringVerify(name = "确认状态",required = true)
    private String status;

}
