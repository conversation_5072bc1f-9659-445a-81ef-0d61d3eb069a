package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 文件类型枚举类
 *
 * <AUTHOR>
 * @since 2023-08-03
 **/
@Getter
public enum FileTypeEnum implements ValueEnum<Integer> {

    /**
     * 图片
     */
    IMAGE(0, "图片"),

    /**
     * 文件
     */
    FILE(1, "文件"),

    /**
     * 视频
     */
    VIDEO(2, "视频");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    FileTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
