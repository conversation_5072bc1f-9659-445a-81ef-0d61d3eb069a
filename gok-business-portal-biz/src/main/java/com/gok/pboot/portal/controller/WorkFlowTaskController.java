package com.gok.pboot.portal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.flowable.task.dto.task.TaskStatusSyncDto;
import com.gok.bcp.flowable.task.vo.TaskDetailVo;
import com.gok.components.common.util.R;
import com.gok.pboot.portal.dto.WorkFlowPageDTO;
import com.gok.pboot.portal.service.IWorkFlowTaskService;
import com.gok.pboot.portal.vo.WorkFlowTaskVo;
import com.gok.pboot.portal.vo.WorkflowTaskTotal;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 任务控制器
 *
 * <AUTHOR>
 * @date 27/10/2023
 * @menu 任务控制器
 */
@RestController
@RequestMapping("/workflow-task")
@RequiredArgsConstructor
public class WorkFlowTaskController {

    private final IWorkFlowTaskService workFlowTaskService;

    /**
     * 分页
     *
     * @param workFlowPageDTO {@link WorkFlowPageDTO} 分页参数
     * @return {@link R}<{@link Page}<{@link WorkFlowTaskVo}>>
     */
    @GetMapping("/page")
    public R<Page<WorkFlowTaskVo>> page(@Valid WorkFlowPageDTO workFlowPageDTO) {
        return workFlowTaskService.getRemotePage(workFlowPageDTO);
    }

    /**
     * 状态个数
     *
     * @return {@link R}<{@link WorkflowTaskTotal>
     */
    @GetMapping("/total")
    public R<WorkflowTaskTotal> total() {
        return workFlowTaskService.total();
    }

    /**
     * 按 ID 获取任务
     *
     * @param id {@link Long} 编号
     * @return {@link R}<{@link TaskDetailVo}>
     */
    @GetMapping("/task/{id}")
    public R<TaskDetailVo> getTaskById(@PathVariable Long id) {
        return workFlowTaskService.getTaskById(id);
    }

    /**
     * 按 ID 获取任务跳转链接
     *
     * @param id {@link Long} 编号
     * @return {@link R}<{@link String}>
     */
    @GetMapping("/workflow/{id}")
    public R<String> getWorkflowById(@PathVariable Long id) {
        return workFlowTaskService.getWorkflowById(id);
    }

    /**
     * 状态同步
     *
     * @param taskStatusSyncDto {@link TaskStatusSyncDto} 任务状态同步
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping
    public R<Boolean> statusSync(@RequestBody TaskStatusSyncDto taskStatusSyncDto) {
        return workFlowTaskService.statusSync(taskStatusSyncDto);
    }

}
