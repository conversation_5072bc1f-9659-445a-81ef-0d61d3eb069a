package com.gok.pboot.portal.vo;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.portal.db.entity.CmsContent;
import com.gok.pboot.portal.enums.BusinessTypeEnum;
import com.gok.pboot.portal.enums.ContentTypeEnum;
import com.gok.pboot.portal.enums.LaunchStatusEnum;
import com.gok.pboot.portal.util.EnumUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容详情 Vo类
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsContentDetailVo {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 上架状态 字典id
     */
    private Integer status;

    /**
     * 上架状态 字典值
     */
    private String statusTxt;

    /**
     * 业务分类 字典id
     * {@link com.gok.pboot.portal.enums.BusinessTypeEnum}
     */
    private Integer businessType;

    /**
     * 业务分类 字典值
     */
    private String businessTypeTxt;

    /**
     * 内容类型 字典id
     * {@link com.gok.pboot.portal.enums.ContentTypeEnum}
     */
    private Integer contentType;

    /**
     * 内容类型 字典值
     */
    private String contentTypeTxt;

    /**
     * 内容类别
     * {@link com.gok.pboot.portal.enums.ContentCategoryEnum}
     */
    @ApiModelProperty(value = "内容类别（0轮播图、1通知公告、2公文、3培训计划、4新闻资讯、5国科周报）")
    private Integer contentCategory;

    /**
     * 发布者id
     */
    private Long publisherId;

    /**
     * 发布者名称
     */
    private String publisherName;

    /**
     * 发布时间（为空则取创建时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime publishTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 正文
     */
    private String contentText;

    /**
     * 关联内容id
     */
    private Long relationContentId;

    /**
     * 链接地址
     */
    private String linkAddress;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 封面图url
     */
    private String coverImageUrl;

    /**
     * 封面图url(不包括http等前缀)
     */
    private String coverImageUrlSuffix;

    /**
     * 发送对象
     * {@link com.gok.pboot.portal.enums.CmsSendObjEnum}
     */
    private Integer sendObj;

    /**
     * ID 列表
     */
    private List<Long> idList;

    /**
     * 发布类型
     */
    private Integer publishType;

    /**
     * 是否发送
     * {@link com.gok.pboot.portal.enums.CmsSendEnum}
     */
    private Integer isSend;

    /**
     * 构建
     *
     * @param cmsContent CMS内容
     * @return {@link CmsContentDetailVo}
     */
    public static CmsContentDetailVo build(CmsContent cmsContent) {
        CmsContentDetailVo cmsContentDetailVo = BeanUtil.copyProperties(cmsContent, CmsContentDetailVo.class);
        cmsContentDetailVo.setStatusTxt(EnumUtils.getNameByValue(LaunchStatusEnum.class, cmsContentDetailVo.getStatus()));
        cmsContentDetailVo.setBusinessTypeTxt(EnumUtils.getNameByValue(BusinessTypeEnum.class, cmsContentDetailVo.getBusinessType()));
        cmsContentDetailVo.setContentTypeTxt(EnumUtils.getNameByValue(ContentTypeEnum.class, cmsContentDetailVo.getContentType()));
        return cmsContentDetailVo;
    }

}
