package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容详情 Vo类
 *
 * <AUTHOR>
 * @since 2023-07-31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentInfoVo {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 上架状态 字典id
     */
    private Integer status;

    /**
     * 上架状态 字典值
     */
    private String statusTxt;

    /**
     * 发布者id
     */
    private Long publisherId;

    /**
     * 发布者名称
     */
    private String publisherName;

    /**
     * 定时发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 业务分类 字典id
     * {@link com.gok.pboot.portal.enums.BusinessTypeEnum}
     */
    private Integer businessType;

    /**
     * 业务分类 字典值
     */
    private String businessTypeTxt;

    /**
     * 内容类型 字典id
     * {@link com.gok.pboot.portal.enums.ContentTypeEnum}
     */
    private Integer contentType;

    /**
     * 封面图url
     */
    private String coverImageUrl;

    /**
     * 内容类型 字典值
     */
    private String contentTypeTxt;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 附件列表
     */
    private List<ContentFileVo> contentFileVoList;
}
