package com.gok.pboot.portal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 内容管理查询 Dto
 *
 * <AUTHOR>
 * @since 2023-08-01
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsContentDto {

    /**
     * 上下架状态 字典id
     */
    private Integer status;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容类别数组
     * {@link com.gok.pboot.portal.enums.ContentCategoryEnum}
     */
    private List<Integer> contentCategoryList;

    /**
     * 业务分类数组
     * {@link com.gok.pboot.portal.enums.BusinessTypeEnum}
     */
    private List<Integer> businessTypeList;

    /**
     * 发布时间
     * <p>
     * yyyy-MM-dd HH:mm:ss 门户首页需根据当前时间过滤未被发布的数据
     */
    private String publishTime;

    /**
     * 是否允许展示标记字段
     * 门户首页用于过滤未编码完成等无法展示的内容信息
     */
    private Integer showFlag;

    /**
     * 排序类型（0 按创建时间排序，1 按发布时间排序）
     */
    private Integer orderType;

    /**
     * 跳过权限查询全部内容，默认为false
     */
    private boolean selAll;

}
