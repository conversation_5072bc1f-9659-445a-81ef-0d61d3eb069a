package com.gok.pboot.portal.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * BUG类型 Enum
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Getter
public enum BugTypeEnum implements ValueEnum<String> {

    /**
     * 代码错误
     */
    CODEERROR("codeerror", "代码错误"),

    /**
     * 配置相关
     */
    CONFIG("config", "配置相关"),

    /**
     * 安装部署
     */
    INSTALL("install", "安装部署"),

    /**
     * 安全相关
     */
    SECURITY("security", "安全相关"),

    /**
     * 性能相关
     */
    PERFORMANCE("performance", "性能相关"),

    /**
     * 标准规范
     */
    STANDARD("standard", "标准规范"),

    /**
     * 测试脚本
     */
    AUTOMATION("automation", "测试脚本"),

    /**
     * 设计缺陷
     */
    DESIGNDEFECT("designdefect", "设计缺陷"),

    /**
     * 其它
     */
    OTHERS("others", "其它");

    private String value;

    private String name;

    BugTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
