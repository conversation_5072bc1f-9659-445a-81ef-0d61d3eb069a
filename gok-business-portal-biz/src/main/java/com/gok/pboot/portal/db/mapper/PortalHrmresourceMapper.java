package com.gok.pboot.portal.db.mapper;


import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.portal.db.entity.PortalHrmResource;
import com.gok.pboot.portal.vo.UserInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PortalHrmresourceMapper extends BaseMapper<PortalHrmResource> {

    UserInfoVo selByUserId(@Param(value = "userId") Long userId);

    UserInfoVo selByMobile(@Param(value = "mobile") String mobile);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号
     * @return 用户信息集合[]
     */
    List<PortalHrmResource> selByPhone(@Param(value = "phone") String phone);

}