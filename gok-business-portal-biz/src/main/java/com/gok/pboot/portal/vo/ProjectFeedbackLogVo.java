package com.gok.pboot.portal.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 项目反馈批转信息vo
 *
 * <AUTHOR>
 * @date 2023/08/25

/**
 * 工单管理--反馈批转信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeedbackLogVo {


    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 时间
     */
    private LocalDateTime createTime;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 操作类型Str
     */
    private String operationTypeStr;

    /**
     * 操作类型
     */
    private Integer operationType;
}
