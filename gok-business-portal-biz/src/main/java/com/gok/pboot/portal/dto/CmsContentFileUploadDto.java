package com.gok.pboot.portal.dto;

import com.gok.pboot.common.core.validation.annotation.ListVerify;
import com.gok.pboot.common.core.validation.annotation.LongVerify;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.util.List;

/**
 * 附件上传Dto类
 *
 * <AUTHOR>
 * @since 2023-08-04
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsContentFileUploadDto {

    /**
     * 内容id
     */
    @LongVerify(name = "内容id", required = true)
    private Long contentId;

    /**
     * 内容附件集合
     */
    @ListVerify(name = "内容附件集合", minSize = 0, maxSize = 20)
    private List<@Valid CmsContentFileUploadDtoItem> fileList;

}
