package com.gok.pboot.portal.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 应用中心-常用应用
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("portal_application_center")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "应用中心-常用应用")
public class PortalApplicationCenter extends Model<PortalApplicationCenter> {


    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;


    /**
     * 用户id
     */
    private Long userId;

    /**
     * 应用id
     */
    private Long applicationId;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;

}
