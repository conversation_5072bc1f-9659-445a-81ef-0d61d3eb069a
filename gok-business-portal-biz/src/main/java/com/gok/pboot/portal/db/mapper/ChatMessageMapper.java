package com.gok.pboot.portal.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.portal.db.entity.DifyChatMessages;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 国科小助手
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface ChatMessageMapper extends BaseMapper<DifyChatMessages> {

    /**
     * 根据用户id查询与国科小助手的聊天记录
     *
     * @param userId 用户id
     * @return 聊天记录
     */
    DifyChatMessages selectByUserId(@Param("userId") Long userId);
}
