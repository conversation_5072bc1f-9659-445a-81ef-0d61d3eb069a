package com.gok.pboot.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.upms.dto.UserOutDTO;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysDeptVo;
import com.gok.bcp.upms.vo.SysOauthClientDetailsVO;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.util.R;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.mapper.SysFileMapper;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.portal.db.entity.*;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackConfirmMapper;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackLogMapper;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackMapper;
import com.gok.pboot.portal.db.mapper.ProjectFeedbackResponseMapper;
import com.gok.pboot.portal.dto.*;
import com.gok.pboot.portal.enums.*;
import com.gok.pboot.portal.service.IProjectFeedbackMessageService;
import com.gok.pboot.portal.service.IProjectFeedbackService;
import com.gok.pboot.portal.util.CdnImgUtils;
import com.gok.pboot.portal.util.EnumUtils;
import com.gok.pboot.portal.util.ZenTaoUtils;
import com.gok.pboot.portal.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目反馈的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Service
@Slf4j
public class ProjectFeedbackServiceImpl extends ServiceImpl<ProjectFeedbackMapper, ProjectFeedback> implements IProjectFeedbackService {

    @Autowired
    private ProjectFeedbackLogMapper projectFeedbackLogMapper;

    @Autowired
    private ProjectFeedbackResponseMapper projectFeedbackResponseMapper;

    @Autowired
    private ProjectFeedbackMapper projectFeedbackMapper;

    @Value("${zen-tao.bugurl}")
    private String bugUrl;

    @Value("${recent-router.feedback.front}")
    private String recentRouterFront;

    @Value("${recent-router.feedback.back}")
    private String recentRouterBack;

    @Autowired
    private ProjectFeedbackConfirmMapper projectFeedbackConfirmMapper;

    @Resource
    private RemoteOutService remoteOutService;

    @Autowired
    private IProjectFeedbackMessageService projectFeedbackMessageService;

    @Resource
    private ZenTaoUtils zenTaoUtils;

    @Resource
    private CdnImgUtils cdnImgUtils;

    @Autowired
    private SysFileService sysFileService;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private RemoteMailService remoteMailService;

    /**
     * 反馈不存在
     */
    public static final String NOT_EXIST = "反馈不存在";

    /**
     * 搁置文本
     */
    public static final String SHELVE_STR = "您好！很抱歉您遇到的问题不能得到及时解决。经过仔细审核和评估，我们不得不搁置您的反馈。";


    @Override
    public R<Page<ProjectFeedbackVo>> queryFeedbackByCreate(Page page, String content) {
        LambdaQueryWrapper<ProjectFeedback> queryWrapper = new LambdaQueryWrapper<>();
        //根据内容模糊查询
        queryWrapper.like(StrUtil.isNotBlank(content), ProjectFeedback::getContent, content);

        queryWrapper.eq(ProjectFeedback::getCreateBy, SecurityUtils.getUser().getUsername())
                .orderByDesc(ProjectFeedback::getCreateTime); // 按照反馈时间倒序排列
        //获取实体类分页
        page(page, queryWrapper);
        //拷贝实体类分页到VO类的分页
        List<ProjectFeedback> records = page.getRecords();
        List<ProjectFeedbackVo> collect = records.stream().map(e -> {
            ProjectFeedbackVo vo = BeanUtil.copyProperties(e, ProjectFeedbackVo.class);
            vo.setHandlingSituationStr(FeedbackHandlingSituationEnum.getNameByValue(e.getHandlingSituation()));
            vo.setTypeStr(FeedbackType.getNameByValue(e.getType()));
            //是否评价过了
            if (e.getServiceAttitude() != null) {
                vo.setEvaluated(1);
            }
            return vo;
        }).collect(Collectors.toList());
        page.setRecords(collect);
        return R.ok(page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R removeProjectFeedback(Long id) {
        ProjectFeedback projectFeedback = getById(id);
        if (projectFeedback == null) {
            return R.failed(NOT_EXIST);
        }
        if (!FeedbackHandlingSituationEnum.CONFIRM.getValue().equals(projectFeedback.getHandlingSituation()) && !FeedbackHandlingSituationEnum.PROCESSING.getValue().equals(projectFeedback.getHandlingSituation())) {
            return R.failed("只有待处理和处理中的反馈可以取消");
        }
        projectFeedback.setHandlingSituation(FeedbackHandlingSituationEnum.CANCEL.getValue());
        projectFeedback.setSortOrder(FeedbackSortOrderEnum.CANCEL.getValue());
        updateById(projectFeedback);
        //插入批转表
        insertProjectFeedbackLog(projectFeedback.getId(), FeedbackOperationTypeEnum.CANCEL.getValue(), FeedbackOperationTypeEnum.CANCEL.getName(), SecurityUtils.getUser().getName());
        //插入消息表
        StringBuilder sb = new StringBuilder();
        sb.append(SHELVE_STR);
        projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.CANCEL.getValue(), projectFeedback.getId(), SecurityUtils.getUser().getName(), SecurityUtils.getUser().getId(), sb.toString(), null);
        return R.ok("取消成功");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R closeProjectFeedback(Long id) {
        ProjectFeedback projectFeedback = getById(id);
        if (projectFeedback == null) {
            return R.failed(NOT_EXIST);
        }
        if (!FeedbackHandlingSituationEnum.SHELVE.getValue().equals(projectFeedback.getHandlingSituation()) && !FeedbackHandlingSituationEnum.RESOLVED.getValue().equals(projectFeedback.getHandlingSituation())) {
            return R.failed("只有搁置和已解决的反馈可以关闭");
        }
        projectFeedback.setHandlingSituation(FeedbackHandlingSituationEnum.CLOSED.getValue());
        projectFeedback.setSortOrder(FeedbackSortOrderEnum.CLOSED.getValue());
        updateById(projectFeedback);
        insertProjectFeedbackLog(projectFeedback.getId(), FeedbackOperationTypeEnum.CLOSED.getValue(), FeedbackOperationTypeEnum.CLOSED.getName(), SecurityUtils.getUser().getName());
        return R.ok("关闭成功");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R saveDto(ProjectFeedbackSaveDto dto) {
        ProjectFeedback projectFeedback = BeanUtil.copyProperties(dto, ProjectFeedback.class);
        //基础字段赋值
        long id = IdWorker.getId();
        projectFeedback.setId(id);
        projectFeedback.setSortOrder(0);
        PigxUser user = SecurityUtils.getUser();
        projectFeedback.setFeedbackUserId(user.getId());
        projectFeedback.setFeedbackUserName(user.getName());
        projectFeedbackMapper.insert(projectFeedback);
        //插入反馈消息
        String messageContent = jointMessageContent(dto.getType(), dto.getContent());
        StringBuilder sb = new StringBuilder();
        sb.append(user.getName()).append("提交了").append(messageContent).append("请及时关注。");
        projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.SUBMIT.getValue(), projectFeedback.getId(), user.getName(), user.getId(), sb.toString(), null);
        //消息推送
        ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
        //运营人员为闫文雯
        BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
                .targetId("31").targetName("闫文雯").build();
        targetList.add(targetDTO);
        syncToPlatform("反馈提交", sb.toString(), TargetTypeEnum.USERS.getValue(), targetList, recentRouterBack + id, SourceEnum.PORTAL_BACK);

        return R.ok("插入成功");
    }

    @Override
    public R getDetail(Long id) {
        ProjectFeedback feedback = this.getById(id);
        if (feedback == null) {
            return R.ok();
        }
        //1.赋值反馈信息
        ProjectFeedbackDetailVo projectFeedbackDetailVo = BeanUtil.copyProperties(feedback, ProjectFeedbackDetailVo.class);
        projectFeedbackDetailVo.setHandlingSituationStr(FeedbackHandlingSituationEnum.getNameByValue(feedback.getHandlingSituation()));
        projectFeedbackDetailVo.setTypeStr(FeedbackType.getNameByValue(feedback.getType()));
        projectFeedbackDetailVo.setEvaluated(feedback.getServiceAttitude() == null ? null : 1);
        //获得反馈信息相关图片url
        if (ArrayUtil.isNotEmpty(feedback.getFileIds())) {
            String filesIds = StrUtil.isEmpty(feedback.getFileIds()) ? null : feedback.getFileIds();
            List<String> urlList = bitchChangeUrl(sysFileService.getUrlList(filesIds));
            projectFeedbackDetailVo.setPicture(urlList);
        }
        //2.赋值批转信息
        List<ProjectFeedbackLog> projectFeedbackLogs = projectFeedbackLogMapper.queryProjectFeedbackLogByFeedbackID(id);
        if (ArrayUtil.isNotEmpty(projectFeedbackLogs)) {
            List<ProjectFeedbackLogVo> projectFeedbackLogVos = projectFeedbackLogs.stream().map(e -> {
                ProjectFeedbackLogVo projectFeedbackLogVo = BeanUtil.copyProperties(e, ProjectFeedbackLogVo.class);
                projectFeedbackLogVo.setOperationTypeStr(FeedbackOperationTypeEnum.getNameByValue(e.getOperationType()));
                return projectFeedbackLogVo;
            }).collect(Collectors.toList());
            projectFeedbackDetailVo.setProjectFeedbackLogVoList(projectFeedbackLogVos);
        }
        //3.赋值反馈回复
        ProjectFeedbackResponse projectFeedbackResponse = projectFeedbackResponseMapper.queryProjectFeedbackResponseByFeedbackID(id);
        if (projectFeedbackResponse != null) {
            ProjectFeedbackResponseVo projectFeedbackResponseVo = BeanUtil.copyProperties(projectFeedbackResponse, ProjectFeedbackResponseVo.class);
            //获得反馈回复的图片url
            if (ArrayUtil.isNotEmpty(projectFeedbackResponse.getFileIds())) {
                String filesIds = StrUtil.isEmpty(projectFeedbackResponse.getFileIds()) ? null : projectFeedbackResponse.getFileIds();
                List<String> urlListRes = bitchChangeUrl(sysFileService.getUrlList(filesIds));
                projectFeedbackResponseVo.setPicture(urlListRes);
            }
            projectFeedbackDetailVo.setProjectFeedbackResponseVo(projectFeedbackResponseVo);
        }
        return R.ok(projectFeedbackDetailVo);
    }


    @Override
    public R satisfaction(ProjectFeedbackSatisfactionDto projectFeedbackSatisfactionDto) {
        ProjectFeedback projectFeedback = this.getById(projectFeedbackSatisfactionDto.getId());
        if (projectFeedback == null) {
            return R.failed(NOT_EXIST);
        }
        if (projectFeedback.getServiceAttitude() != null) {
            return R.failed("一个工单仅允许评价一次");
        }
        Integer handlingSituation = projectFeedback.getHandlingSituation();
        if (FeedbackHandlingSituationEnum.CONFIRM.getValue().equals(handlingSituation) || FeedbackHandlingSituationEnum.PROCESSING.getValue().equals(handlingSituation)) {
            return R.failed("该反馈还不能提交满意度");
        }
        projectFeedbackMapper.setSatisfaction(projectFeedbackSatisfactionDto);
        return R.ok("满意度提交成功");
    }

    @Override
    public R workOrderPage(Page page, WorkOrderPageDto workOrderPage) {
        projectFeedbackMapper.queryWOList(page, workOrderPage);
        List records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            //添加HandlingSituationStr，TypeStr
            records.forEach(r -> {
                WorkOrderPageVo pageVo = Convert.convert(WorkOrderPageVo.class, r);
                pageVo.setHandlingSituationStr(FeedbackHandlingSituationEnum.getNameByValue(pageVo.getHandlingSituation()));
                pageVo.setTypeStr(FeedbackType.getNameByValue(pageVo.getType()));
                r = pageVo;
            });
        }
        return R.ok(page);
    }

    @Override
    public R workOrderDetailUpdate(WorkOrderDetailUpdateDto dto) {
        boolean res = projectFeedbackConfirmMapper.updateByFeedBackId(dto.getFollowedWorkerId(), dto.getFollowedWorker(),
                SecurityUtils.getUser().getName(), dto.getFeedbackId());
        if (res) {
            return R.ok("更新成功");
        } else {
            return R.failed("更新失败");
        }
    }

    @Override
    public R<List<SysUserOutVO>> userLikeList(String userName) {
        UserOutDTO dto = new UserOutDTO();
        dto.setUsername(userName);
        List<SysUserOutVO> list = remoteOutService.getUserListByMultiParameter(dto).getData();

        log.error(SecurityUtils.getUser().getName());

        return R.ok(list);
    }

    @Override
    public List<UnclosedBugsVo> getUnclosedBugs() {
        final String status = "status";
        final String closed = "closed";
        // 查询处理中的反馈任务
        List<ProjectFeedback> projectFeedbackList = projectFeedbackMapper.selNotClosed();
        List<UnclosedBugsVo> unclosedBugsVos = new ArrayList<>();

        if (CollectionUtils.isEmpty(projectFeedbackList)) {
            return unclosedBugsVos;
        }
        Map<Long, ProjectFeedback> feedbackMap = projectFeedbackList.stream().collect(Collectors.toMap(ProjectFeedback::getId, a -> a));

        List<Long> feedbackIds = projectFeedbackList.stream().map(e -> {
            //根据反馈id查询 反馈确认 数据
            ProjectFeedbackConfirm feedbackConfirm = projectFeedbackConfirmMapper.getOneByFeedbackId(e.getId());
            if (Optional.ofNullable(feedbackConfirm).isPresent()) {
                //获取禅道ID
                Long zentaoId = feedbackConfirm.getZentaoId();
                Map<String, Object> bugDetail = zenTaoUtils.getBugDetail(zentaoId);
                //获取bug的状态
                String bugStatus = String.valueOf(bugDetail.get(status));
                // 获取是否转为需求
                String isToStory = String.valueOf(bugDetail.get("story"));
                log.info("禅道Bug是否转为需求:{}", isToStory);
                Long storyId = NumberUtils.LONG_MINUS_ONE;
                if (NumberUtils.INTEGER_ZERO.toString().equals(isToStory)) {
                    storyId = Long.valueOf(String.valueOf(bugDetail.get("toStory")));
                    log.info("禅道Bug转为需求的id:{}", storyId);
                }
                // 根据需求id判断是否关闭
                String storyStatus = StrUtil.EMPTY;
                if (!NumberUtils.LONG_MINUS_ONE.equals(storyId)) {
                    Map<String, Object> storeDetail = zenTaoUtils.getStoreDetail(storyId);
                    storyStatus = String.valueOf(storeDetail.get(status));
                    log.info("禅道需求状态:{}", storyStatus);
                }
                // 同步修改 反馈 的状态为已处理 且根据当前指派人插入反馈数据
                // 不转为需求的前提下BUG为关闭状态进行关闭、转为需求后判断需求状态的状态是否为关闭
                if ("null".equals(storyStatus) && closed.equals(bugStatus)
                        || closed.equals(storyStatus)) {
                    projectFeedbackMapper.updateStatusByFeedbackId(e.getId());
                    // 获取当前指派人
                    ProjectFeedbackConfirm projectFeedbackConfirm = projectFeedbackConfirmMapper.getOneByFeedbackId(e.getId());
                    if (Optional.ofNullable(projectFeedbackConfirm).isPresent()) {
                        this.insertProjectFeedbackLog(e.getId(), FeedbackOperationTypeEnum.PROCESSED.getValue(),
                                FeedbackOperationTypeEnum.PROCESSED.getName(), projectFeedbackConfirm.getHandleWorker());
                    }
                    //同步消息到中台，发送给跟进人
                    log.info("关闭任务同步推送消息到中台{}", e);
                    String content = jointMessageContent(e.getType(), e.getContent()) + "已得到解决，请及时关注跟进。";
                    ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
                    targetList.add(BcpMessageTargetDTO.builder().targetId(projectFeedbackConfirm.getFollowedWorkerId().toString()).targetName(projectFeedbackConfirm.getFollowedWorker()).build());
                    closedSyncToPlatform("反馈已处理", content, TargetTypeEnum.USERS.getValue(), targetList, recentRouterBack + projectFeedbackConfirm.getFeedbackId(), e);
                    return e.getId();
                }
            }
            return null;
        }).collect(Collectors.toList());

        unclosedBugsVos = feedbackIds.stream().filter(o -> Optional.ofNullable(o).isPresent()).map(f -> {
            ProjectFeedback projectFeedback = feedbackMap.get(f);
            return UnclosedBugsVo.builder()
                    .id(f)
                    .content(projectFeedback.getContent())
                    .handlingSituation(projectFeedback.getHandlingSituation())
                    .handlingSituationStr(FeedbackHandlingSituationEnum.getNameByValue(projectFeedback.getHandlingSituation()))
                    .build();
        }).collect(Collectors.toList());
        return unclosedBugsVos;
    }

    @Override
    public R<WorkOrderVo> showWorkOrder(Long id) {
        //1.校验id是否存在
        ProjectFeedback projectFeedback = this.getById(id);
        if (ObjectUtil.isEmpty(projectFeedback)) {
            return R.failed("该反馈不存在");
        }


        WorkOrderVo workOrderVo = new WorkOrderVo();
        //2.将反馈表中的有用信息拷贝到vo类
        BeanUtil.copyProperties(projectFeedback, workOrderVo);
        workOrderVo.setFeedbackCreateTime(projectFeedback.getCreateTime());
        String voFilesIds = StrUtil.isEmpty(projectFeedback.getFileIds()) ? null : projectFeedback.getFileIds();
        workOrderVo.setFeedbackFileIds(sysFileService.getUrlList(voFilesIds));
        workOrderVo.setFeedbackFileIds(bitchChangeUrl(workOrderVo.getFeedbackFileIds()));
        workOrderVo.setFeedbackCreateBy(projectFeedback.getFeedbackUserName());
        workOrderVo.setTypeStr(FeedbackType.getNameByValue(projectFeedback.getType()));
        workOrderVo.setHandlingSituationStr(FeedbackHandlingSituationEnum.getNameByValue(projectFeedback.getHandlingSituation()));

        //3.设置部门
        String dept = getDept(projectFeedback.getFeedbackUserId());
        workOrderVo.setDept(dept);

        //4.设置批转信息
        List<ProjectFeedbackLogVo> feedbackLogVos = projectFeedbackLogMapper.queryAllByFeedbackId(id);
        for (ProjectFeedbackLogVo vo : feedbackLogVos) {
            vo.setOperationTypeStr(FeedbackOperationTypeEnum.getNameByValue(vo.getOperationType()));
        }
        workOrderVo.setLogVos(feedbackLogVos);

        //5.设置回复信息
        ProjectFeedbackResponse response = projectFeedbackResponseMapper.getOneByFeedbackId(id);
        if (response != null) {
            workOrderVo.setResponseContent(response.getResponseContent());
            workOrderVo.setResponseCreateTime(response.getCreateTime());
            String filesIds = StrUtil.isEmpty(response.getFileIds()) ? null : response.getFileIds();
            workOrderVo.setResponseFileIds(sysFileService.getUrlList(filesIds));
            workOrderVo.setResponseFileIds(bitchChangeUrl(workOrderVo.getResponseFileIds()));
        }

        //6.设置确定表信息
        ProjectFeedbackConfirm confirm = projectFeedbackConfirmMapper.getOneByFeedbackId(id);
        BeanUtil.copyProperties(confirm, workOrderVo);

        return R.ok(workOrderVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R doWorkOrder(WorkOrderDto dto) {
        //1.校验id是否存在
        ProjectFeedback projectFeedback = this.getById(dto.getFeedbackId());
        if (ObjectUtil.isEmpty(projectFeedback)) {
            return R.failed("该反馈不存在");
        }
        //   判断操作合理性
        boolean judge = this.wordOrderJudge(projectFeedback.getHandlingSituation(), dto.getOperationType());
        if (!judge) {
            return R.failed("操作不规范");
        }
        // 确认情况的判断
        boolean nullJudge = this.nullJudge(dto.getConfirmDto(), dto.getOperationType());
        if (!nullJudge) {
            return R.ok("传入数据不全");
        }

        //如果是一开始是搁置

        //2.修改确认表
        ProjectFeedbackConfirm feedbackConfirm = projectFeedbackConfirmMapper.getOneByFeedbackId(dto.getFeedbackId());
        if (feedbackConfirm == null && dto.getConfirmDto() != null || (Objects.equals(dto.getOperationType(), FeedbackOperationTypeEnum.SHELVE.getValue()) && feedbackConfirm == null)) {
            feedbackConfirm = new ProjectFeedbackConfirm();
            feedbackConfirm.setCreateTime(LocalDateTime.now());
            feedbackConfirm.setFeedbackId(dto.getFeedbackId());
            if (dto.getConfirmDto() != null && dto.getConfirmDto().getRemarks() != null && dto.getConfirmDto().getRemarks().length() > 500) {
                return R.failed("备注字数超过500");
            }
            BeanUtil.copyProperties(dto.getConfirmDto(), feedbackConfirm);
            //2.1 创建保存bug
            createAndSaveBug(dto, projectFeedback, feedbackConfirm);

            try {
                projectFeedbackConfirmMapper.insert(feedbackConfirm);
            } catch (Exception ignored) {
                return R.failed("提交反馈未确定,无法继续执行");
            }
        } else if (dto.getConfirmDto() != null) {
            feedbackConfirm.setUpdateBy(SecurityUtils.getUser().getName());
            feedbackConfirm.setUpdateTime(LocalDateTime.now());
            BeanUtil.copyProperties(dto.getConfirmDto(), feedbackConfirm);
            projectFeedbackConfirmMapper.updateById(feedbackConfirm);
        }


        //3.修改回复表（判断是否是已解决或者是搁置状态）
        if (dto.getResponseDto() != null && (Objects.equals(dto.getOperationType(), FeedbackOperationTypeEnum.SHELVE.getValue())
                || Objects.equals(dto.getOperationType(), FeedbackOperationTypeEnum.RESOLVED.getValue()))) {
            if (dto.getResponseDto().getResponseContent() != null && dto.getResponseDto().getResponseContent().length() > 500) {
                return R.failed("回复信息超过字数超过500");
            }
            saveOrUpdateResponse(dto.getFeedbackId(), dto.getResponseDto().getResponseContent(), dto.getResponseDto().getResponseFileIds());
        }

        //4.新增批转记录表数据
        if (dto.getConfirmDto() == null) {
            this.insertProjectFeedbackLog(dto.getFeedbackId(), dto.getOperationType(), FeedbackOperationTypeEnum.getNameByValue(dto.getOperationType()), SecurityUtils.getUser().getName());
        } else {
            this.insertProjectFeedbackLog(dto.getFeedbackId(), dto.getOperationType(), FeedbackOperationTypeEnum.getNameByValue(dto.getOperationType()), dto.getConfirmDto().getFollowedWorker());
        }

        //5.修改反馈表状态
        if (dto.getOperationType() > 0 && dto.getOperationType() < 7) {
            projectFeedback.setHandlingSituation(dto.getOperationType());
        } else if (dto.getOperationType().equals(FeedbackOperationTypeEnum.REACTIVATION.getValue())) {
            projectFeedback.setHandlingSituation(FeedbackHandlingSituationEnum.CONFIRM.getValue());
            //删除旧的confirm
            LambdaQueryWrapper<ProjectFeedbackConfirm> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProjectFeedbackConfirm::getFeedbackId, projectFeedback.getId());
            projectFeedbackConfirmMapper.delete(wrapper);
            //重新激活发送消息给阎文雯
            String messageContent = jointMessageContent(FeedbackHandlingSituationEnum.CONFIRM.getValue(), projectFeedback.getContent());
            StringBuilder sb = new StringBuilder();
            sb.append(projectFeedback.getFeedbackUserName()).append("提交了").append(messageContent).append("请及时关注。");
            ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
            BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
                    .targetId("31").targetName("闫文雯").build();
            targetList.add(targetDTO);
            syncToPlatform("反馈提交", sb.toString(), TargetTypeEnum.USERS.getValue(), targetList, recentRouterBack + projectFeedback.getId(), SourceEnum.PORTAL_BACK);
        } else if (dto.getOperationType().equals(FeedbackOperationTypeEnum.ROLLBACK.getValue())) {
            projectFeedback.setHandlingSituation(FeedbackHandlingSituationEnum.PROCESSING.getValue());
            //回退到处理中需要推送消息
            sendMessage(projectFeedback, feedbackConfirm, FeedbackHandlingSituationEnum.PROCESSING.getValue(), dto.getFeedbackId());
            //回退激活禅道bug

            Map<String, Object> activeBug = zenTaoUtils.activeBug(feedbackConfirm.getZentaoId(), feedbackConfirm.getHandleWorkerId());
            String status = String.valueOf(activeBug.get("status"));
            log.info("回退激活禅道bug，bug状态{}", status);

        }
        if (dto.getConfirmDto() != null && dto.getConfirmDto().getType() != null) {
            projectFeedback.setType(dto.getConfirmDto().getType());
        }

        projectFeedbackMapper.updateById(projectFeedback);
        //6.发送消息功能
        this.sendMessage(projectFeedback, feedbackConfirm, dto.getOperationType(), dto.getFeedbackId());

        return R.ok("操作成功");
    }

    /**
     * 创建 禅道bug 并且赋值到消息表
     *
     * @param dto             DTO
     * @param projectFeedback 项目反馈
     * @param feedbackConfirm 反馈确认
     */
    private void createAndSaveBug(WorkOrderDto dto, ProjectFeedback projectFeedback, ProjectFeedbackConfirm feedbackConfirm) {
//        判断是有禅道地址
        if (!Objects.equals(dto.getOperationType(), FeedbackOperationTypeEnum.CONFIRM.getValue()) || feedbackConfirm.getZentaoUrl() != null) {
            return;
        }
        Integer value = ZenTaoProjectEnum.getValueByApplicationName(projectFeedback.getApplicationName());

        String step = projectFeedback.getContent();
        List<String> idList = this.idsChangeList(projectFeedback.getFileIds());

        if (CollUtil.isNotEmpty(idList)) {
            List<SysFile> sysFiles = sysFileMapper.selectBatchIds(idList);

            for (SysFile sysFile : sysFiles) {
                step = step + "<br/> <img src=\"" + cdnImgUtils.createHttpUrl(sysFile.getFileName()) + "\">";
            }
        }

        Map<String, Object> canDao = zenTaoUtils.createBug(value, projectFeedback.getContent(),
                feedbackConfirm.getUrgencyDegree(), FeedbackUrgencyDegreeEnum.I.getValue(), FeedbackType.getNameByValue(dto.getConfirmDto().getType()),
                dto.getConfirmDto().getHandleWorkerId(), step);
        log.error(canDao.toString());
        feedbackConfirm.setZentaoUrl(bugUrl + canDao.get("id") + ".html");
        Object object = canDao.get("id");
        String canDaoId = object.toString();
        feedbackConfirm.setZentaoId(Long.parseLong(canDaoId));
    }

    @Override
    public List<ApplicationIconVo> getAppBox() {
        List<SysOauthClientDetailsVO> appDate = remoteOutService.getAppList().getData();
        List<ApplicationIconVo> appBox = new ArrayList<>();
        appBox.add(new ApplicationIconVo(-1L, "其他"));
        for (SysOauthClientDetailsVO vo : appDate) {
            if (EnumUtils.getEnumByName(ZenTaoProjectEnum.class, vo.getName()) != null) {
                ApplicationIconVo iconVo = new ApplicationIconVo();
                iconVo.setApplicationName(vo.getName());
                iconVo.setApplicationId(vo.getId());
                appBox.add(iconVo);
            }
        }
        return appBox;
    }


    boolean nullJudge(WorkOrderConfirmDto dto, Integer operationType) {
        if (dto == null) {
            return true;
        }
        if (Objects.equals(operationType, FeedbackOperationTypeEnum.CONFIRM.getValue())) {
            return dto.getType() != null && dto.getHandleWorkerId() != null && dto.getHandleWorker() != null &&
                    dto.getFollowedWorker() != null && dto.getFollowedWorkerId() != null && dto.getUrgencyDegree() != null;
        }
        return true;
    }

    /**
     * 发送的消息
     *
     * @param projectFeedback 反馈信息
     * @param operationType   操作类型
     */
    void sendMessage(ProjectFeedback projectFeedback, ProjectFeedbackConfirm confirm, Integer operationType, Long id) {
        String content;
        if (Objects.equals(operationType, FeedbackOperationTypeEnum.CONFIRM.getValue())) {
            //1.发送给反馈人
            content = "非常感谢您提交反馈。我们已经收到并且已成功受理。我们会尽快为您安排专业的人员进行处理。请耐心等待，我们将会及时跟进和解决您的问题。";
            projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.CONFIRM.getValue(), projectFeedback.getId(),
                    projectFeedback.getFeedbackUserName(), projectFeedback.getFeedbackUserId(), content, confirm.getZentaoUrl());
            //同步推送到中台
            ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
            BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
                    .targetId(projectFeedback.getFeedbackUserId().toString()).targetName(projectFeedback.getFeedbackUserName()).build();
            targetList.add(targetDTO);
            syncToPlatform("反馈确认", content, TargetTypeEnum.USERS.getValue(), targetList, recentRouterFront + id, SourceEnum.PORTAL_FRONT);

            //2.发送给处理人
            content = "您有新的反馈工单待处理，请及时关注。";
            projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.CONFIRM.getValue(), projectFeedback.getId(),
                    confirm.getHandleWorker(), confirm.getHandleWorkerId(), content, confirm.getZentaoUrl());
            //同步推送到中台
            ArrayList<BcpMessageTargetDTO> targetHandleList = new ArrayList<>();
            BcpMessageTargetDTO targetDTO1 = BcpMessageTargetDTO.builder()
                    .targetId(confirm.getHandleWorkerId().toString())
                    .targetName(confirm.getHandleWorker()).build();
            targetHandleList.add(targetDTO1);
            syncToPlatform("反馈确认", content, TargetTypeEnum.USERS.getValue(), targetHandleList, confirm.getZentaoUrl(), SourceEnum.PORTAL_BACK);

            //3.发送给跟进人，同步推送到中台
            ProjectFeedbackMessage projectFeedbackMessage = projectFeedbackMessageService.getByFeedbackIdAndType(confirm.getFeedbackId(), FeedbackTriggerTypeEnum.SUBMIT.getValue());

            ArrayList<BcpMessageTargetDTO> targetFollowList = new ArrayList<>();
            BcpMessageTargetDTO targetDTO2 = BcpMessageTargetDTO.builder()
                    .targetId(confirm.getFollowedWorkerId().toString())
                    .targetName(confirm.getFollowedWorker()).build();
            targetFollowList.add(targetDTO2);
            syncToPlatform("反馈确认", projectFeedbackMessage.getMessageContent(), TargetTypeEnum.USERS.getValue(), targetFollowList, recentRouterBack + id, SourceEnum.PORTAL_BACK);

        } else if (Objects.equals(operationType, FeedbackOperationTypeEnum.CANCEL.getValue())) {
            content = SHELVE_STR;
            projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.CANCEL.getValue(),
                    projectFeedback.getId(), projectFeedback.getFeedbackUserName(), projectFeedback.getFeedbackUserId(), content, null);
            //同步推送到中台（取消动作，先去掉。本次不做推送）
//            ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
//            BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
//                    .targetId(projectFeedback.getFeedbackUserId().toString()).targetName(projectFeedback.getFeedbackUserName()).build();
//            targetList.add(targetDTO);
//            syncToPlatform("反馈取消",content,TargetTypeEnum.USERS.getValue(),targetList,recentRouterFront +id);

        } else if (Objects.equals(operationType, FeedbackOperationTypeEnum.SHELVE.getValue())) {
            content = SHELVE_STR;
            projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.SHELVE.getValue(),
                    projectFeedback.getId(), projectFeedback.getFeedbackUserName(), projectFeedback.getFeedbackUserId(), content, null);
            //同步推送到中台
            ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
            BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
                    .targetId(projectFeedback.getFeedbackUserId().toString()).targetName(projectFeedback.getFeedbackUserName()).build();
            targetList.add(targetDTO);
            syncToPlatform("反馈搁置", content, TargetTypeEnum.USERS.getValue(), targetList, recentRouterFront + id, SourceEnum.PORTAL_FRONT);

        } else if (Objects.equals(operationType, FeedbackOperationTypeEnum.PROCESSED.getValue())) {
            content = jointMessageContent(projectFeedback.getType(), projectFeedback.getContent()) + "已得到解决，请及时关注跟进。";
            projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.PROCESSED.getValue(), projectFeedback.getId(), "闫文雯", 31L, content, null);
            //同步推送到中台
            ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
//            targetList.add(BcpMessageTargetDTO.builder().targetId("31").targetName("闫文雯").build());
            targetList.add(BcpMessageTargetDTO.builder().targetId(confirm.getFollowedWorkerId().toString()).targetName(confirm.getFollowedWorker()).build());
            syncToPlatform("反馈已处理", content, TargetTypeEnum.USERS.getValue(), targetList, recentRouterBack + id, SourceEnum.PORTAL_BACK);
        } else if (Objects.equals(operationType, FeedbackOperationTypeEnum.RESOLVED.getValue())) {
            content = "您提交的" + jointMessageContent(projectFeedback.getType(), projectFeedback.getContent()) + "已得到解决并得到回复。点击查看详细并进行服务评价。请确认反馈是否得到解决，已解决可关闭工单，7 天内未操作的系统将自动关闭";
            projectFeedbackMessageService.insertMassage(FeedbackTriggerTypeEnum.SOLVE.getValue(),
                    projectFeedback.getId(), projectFeedback.getFeedbackUserName(), projectFeedback.getFeedbackUserId(), content, null);
            //同步推送到中台
            ArrayList<BcpMessageTargetDTO> targetList = new ArrayList<>();
            BcpMessageTargetDTO targetDTO = BcpMessageTargetDTO.builder()
                    .targetId(projectFeedback.getFeedbackUserId().toString()).targetName(projectFeedback.getFeedbackUserName()).build();
            targetList.add(targetDTO);
            syncToPlatform("反馈已解决", content, TargetTypeEnum.USERS.getValue(), targetList, recentRouterFront + id, SourceEnum.PORTAL_FRONT);

        }
    }


    /**
     * 判断是否合理的操作
     *
     * @param a 批转流程
     * @param b 处理情况
     * @return 是否合理
     */
    boolean wordOrderJudge(Integer a, Integer b) {
        if (Objects.equals(a, FeedbackHandlingSituationEnum.CONFIRM.getValue()) && (Objects.equals(b, FeedbackOperationTypeEnum.CONFIRM.getValue()) || Objects.equals(b, FeedbackOperationTypeEnum.CANCEL.getValue()) || Objects.equals(b, FeedbackOperationTypeEnum.SHELVE.getValue()))) {
            return true;
        } else if (Objects.equals(a, FeedbackHandlingSituationEnum.PROCESSING.getValue()) && Objects.equals(b, FeedbackOperationTypeEnum.PROCESSED.getValue())) {
            return true;
        } else if (Objects.equals(a, FeedbackHandlingSituationEnum.CANCEL.getValue()) && Objects.equals(b, FeedbackOperationTypeEnum.REACTIVATION.getValue())) {
            return true;
        } else if (Objects.equals(a, FeedbackHandlingSituationEnum.SHELVE.getValue()) && Objects.equals(b, FeedbackOperationTypeEnum.REACTIVATION.getValue())) {
            return true;
        } else if (Objects.equals(a, FeedbackHandlingSituationEnum.PROCESSED.getValue()) && Objects.equals(b, FeedbackOperationTypeEnum.RESOLVED.getValue()) || Objects.equals(b, FeedbackOperationTypeEnum.ROLLBACK.getValue())) {
            return true;
        } else {
            return Objects.equals(a, FeedbackHandlingSituationEnum.RESOLVED.getValue()) && Objects.equals(b, FeedbackOperationTypeEnum.CLOSED.getValue());
        }
    }


    /**
     * 拼接部门名称
     *
     * @param userId 用户Id
     * @return 部门名称
     */
    private String getDept(Long userId) {
        SysUserVo data = remoteOutService.getUserInfoById(userId).getData();
        if (data == null) {
            return "非员工";
        }
        List<SysDeptVo> deptList = data.getDeptList();

        if (CollUtil.isEmpty(deptList)) {
            return StrUtil.SPACE;
        }
        String deptName = deptList.stream().max(Comparator.comparingInt(SysDeptVo::getLevel)).map(SysDeptVo::getName).orElse(StrUtil.SPACE);
        return deptName;
    }


    /**
     * 保存或修改回复信息
     *
     * @param feedbackId 反馈Id
     * @param content    回复内容
     * @param fileIds    回复图片
     */

    private void saveOrUpdateResponse(Long feedbackId, String content, String fileIds) {
        ProjectFeedbackResponse response = projectFeedbackResponseMapper.getOneByFeedbackId(feedbackId);
        if (response == null) {
            //1.首次写回复，未搁置
            response = new ProjectFeedbackResponse();
            response.setResponseContent(content);
            response.setFileIds(fileIds);
            response.setCreateBy(SecurityUtils.getUser().getName());
            response.setCreateTime(LocalDateTime.now());
            response.setFeedbackId(feedbackId);
            projectFeedbackResponseMapper.insert(response);
        } else {
            //修改回复信息
            projectFeedbackResponseMapper.updateResponse(feedbackId, content,
                    fileIds, SecurityUtils.getUser().getName());
        }
    }

    /**
     * 修改确认表的紧急情况和批转表记录
     *
     * @param feedbackId       反馈Id
     * @param operationType    批转类型
     * @param operationContent 批转内容
     */
    private void insertProjectFeedbackLog(Long feedbackId, Integer operationType, String operationContent, String name) {
        ProjectFeedbackLog projectFeedbackLog = new ProjectFeedbackLog();
        //设置基础信息
        if (Objects.equals(operationType, FeedbackOperationTypeEnum.CONFIRM.getValue())) {
            //反馈人批转记录
            projectFeedbackLog.setFeedbackId(feedbackId);
            projectFeedbackLog.setOperationContent("接受反馈");
            projectFeedbackLog.setOperationUserName(SecurityUtils.getUser().getName());
            projectFeedbackLog.setCreateTime(LocalDateTime.now());
            projectFeedbackLog.setOperationType(operationType);
            projectFeedbackLogMapper.insert(projectFeedbackLog);
            //跟进人员批转消息
            ProjectFeedbackLog followedWorkerFeedbackLog = new ProjectFeedbackLog();
            followedWorkerFeedbackLog.setFeedbackId(feedbackId);
            followedWorkerFeedbackLog.setOperationContent("跟进中");
            ProjectFeedbackConfirm confirm = projectFeedbackConfirmMapper.getOneByFeedbackId(feedbackId);
            followedWorkerFeedbackLog.setOperationUserName(confirm.getFollowedWorker());
            followedWorkerFeedbackLog.setCreateTime(LocalDateTime.now());
            followedWorkerFeedbackLog.setOperationType(operationType);
            projectFeedbackLogMapper.insert(followedWorkerFeedbackLog);
            //处理人员批转信息
            ProjectFeedbackLog feedbackLog = new ProjectFeedbackLog();
            feedbackLog.setFeedbackId(feedbackId);
            feedbackLog.setOperationContent("处理中");
            feedbackLog.setOperationUserName(confirm.getHandleWorker());
            feedbackLog.setCreateTime(LocalDateTime.now());
            feedbackLog.setOperationType(operationType);
            projectFeedbackLogMapper.insert(feedbackLog);
        } else if (Objects.equals(operationType, FeedbackOperationTypeEnum.RESOLVED.getValue())) {
            projectFeedbackLog.setFeedbackId(feedbackId);
            projectFeedbackLog.setOperationContent("解决");
            projectFeedbackLog.setOperationUserName(name);
            projectFeedbackLog.setCreateTime(LocalDateTime.now());
            //修改反馈批转表记录
            projectFeedbackLog.setOperationType(operationType);
            projectFeedbackLogMapper.insert(projectFeedbackLog);
        } else {
            projectFeedbackLog.setFeedbackId(feedbackId);
            projectFeedbackLog.setOperationContent(operationContent);
            projectFeedbackLog.setOperationUserName(name);
            projectFeedbackLog.setCreateTime(LocalDateTime.now());
            //修改反馈批转表记录
            projectFeedbackLog.setOperationType(operationType);
            projectFeedbackLogMapper.insert(projectFeedbackLog);
        }

    }

    /**
     * 评价特殊的反馈消息
     *
     * @param type    反馈类型
     * @param content 反馈内容
     * @return 拼接后的反馈内容
     */
    private String jointMessageContent(Integer type, String content) {
        if (content.length() > 8) {
            return "\"" + FeedbackType.getNameByValue(type) + "\"-\"" + content.substring(0, 8) + "...\",";
        } else {
            return "\"" + FeedbackType.getNameByValue(type) + "\"-\"" + content + "\",";
        }
    }

    /**
     * 将ids转换成列表
     *
     * @param ids 拼接的id
     * @return
     */
    private List<String> idsChangeList(String ids) {
        if (ids == null) {
            return new ArrayList<>();
        }
        return Arrays.asList(ids.split(","));
    }

    /**
     * 批量操作图片转换
     *
     * @param fileUrls 图片url
     * @return
     */
    private List<String> bitchChangeUrl(List<String> fileUrls) {
        List<String> res = new ArrayList<>();
        if (fileUrls == null || fileUrls.isEmpty()) {
            return res;
        }
        for (String s : fileUrls) {
            res.add(cdnImgUtils.createHttpUrl(s));
        }
        return res;
    }

    /**
     * 同步到中台
     *
     * @param title       标题
     * @param content     内容
     * @param targetType  目标类型
     * @param targetList  目标列表
     * @param redirectUrl 跳转地址
     */
    private void syncToPlatform(String title, String content, String targetType, ArrayList<BcpMessageTargetDTO> targetList, String redirectUrl, SourceEnum sourceEnum) {
        PigxUser user = SecurityUtils.getUser();
        //封装对象
        MailModel mailModel = new MailModel();
        //固定字段
        mailModel.setSource(sourceEnum.getValue());
        mailModel.setSenderId(user.getId());
        mailModel.setSender(user.getName());
        mailModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
        //可变字段
        mailModel.setTitle(title);
        mailModel.setContent(content);
        mailModel.setTargetType(targetType);
        mailModel.setTargetList(targetList);
        mailModel.setRedirectUrl(redirectUrl);
        //发送消息
        remoteMailService.sendMsg(mailModel);
    }

    /**
     * 关闭任务同步到中台
     *
     * @param title       标题
     * @param content     内容
     * @param targetType  目标类型
     * @param targetList  目标列表
     * @param redirectUrl 跳转地址
     */
    private void closedSyncToPlatform(String title, String content, String targetType, ArrayList<BcpMessageTargetDTO> targetList, String redirectUrl, ProjectFeedback projectFeedback) {

        //封装对象
        MailModel mailModel = new MailModel();
        //固定字段
        mailModel.setSource(SourceEnum.PORTAL_BACK.getValue());
        //禅道关闭任务是异步的
        mailModel.setSenderId(projectFeedback.getFeedbackUserId());
        mailModel.setSender(projectFeedback.getFeedbackUserName());
        mailModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
        //可变字段
        mailModel.setTitle(title);
        mailModel.setContent(content);
        mailModel.setTargetType(targetType);
        mailModel.setTargetList(targetList);
        mailModel.setRedirectUrl(redirectUrl);
        //发送消息
        remoteMailService.sendMsg(mailModel);
    }
}




