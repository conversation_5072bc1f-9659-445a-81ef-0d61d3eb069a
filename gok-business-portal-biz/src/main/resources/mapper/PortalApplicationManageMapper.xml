<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalApplicationManageMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.PortalApplicationManage">
        <id column="id" property="id"/>
        <result column="application_name" property="applicationName"/>
        <result column="category_id" property="categoryId"/>
        <result column="belong_application_id" property="belongApplicationId"/>
        <result column="belong_application_name" property="belongApplicationName"/>
        <result column="link_address" property="linkAddress"/>
        <result column="weight" property="weight"/>
        <result column="file_id" property="fileId"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="lunch_time" property="lunchTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <insert id="insertReturnId" parameterType="com.gok.pboot.portal.db.entity.PortalApplicationManage"
            useGeneratedKeys="true" keyProperty="id">
        insert into portal_application_manage
        (id, application_name, category_id, belong_application_id, belong_application_name, link_address, weight,
         file_id, status, remarks,
         create_by, update_by, create_time, update_time, del_flag, lunch_time)
        values (#{id}, #{applicationName}, #{categoryId}, #{belongApplicationId}, #{belongApplicationName},
                #{linkAddress}, #{weight}, #{fileId}, #{status}, #{remarks},
                #{createBy}, #{updateBy}, #{createTime}, #{updateTime}, #{delFlag}, #{lunchTime})
    </insert>

    <select id="queryVosByIds" resultType="com.gok.pboot.portal.vo.ApplicationIconVo">
        select id,application_name,file_id
        from portal_application_manage
        where del_flag=${@<EMAIL>()} and id IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <select id="queryRightApplicationByIds" resultType="com.gok.pboot.portal.vo.ApplicationIconVo">
        SELECT category_id,pam.id application_id,application_name,file_id,file_url,link_address,belong_application_id
        FROM portal_application_manage AS pam LEFT JOIN sys_file AS sf
        ON pam.file_id=sf.id
        WHERE belong_application_id IN
        <foreach collection="applicationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND pam.del_flag=${@<EMAIL>()}
        AND `status`=${@<EMAIL>()}
        ORDER BY weight DESC,pam.lunch_time DESC LIMIT 6;
    </select>
    <select id="queryRightApplicationByLike" resultType="com.gok.pboot.portal.vo.ApplicationIconVo">
        SELECT category_id,pam.id application_id,application_name,file_id,file_url,link_address
        FROM portal_application_manage AS pam LEFT JOIN sys_file AS sf
        ON pam.file_id=sf.id
        WHERE belong_application_id IN
        <foreach collection="applicationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND pam.del_flag=${@<EMAIL>()}
        AND `status`=${@<EMAIL>()}
        AND application_name LIKE concat('%',#{applicationName},'%')
        ORDER BY weight DESC,pam.create_time DESC
    </select>
    <select id="queryOffShelf" resultType="com.gok.pboot.portal.vo.PortalApplicationManageBoxVo">
        SELECT belong_application_id id, belong_application_name application_name, link_address
        FROM portal_application_manage
        WHERE `status` = ${@<EMAIL>()}
          AND del_flag = ${@<EMAIL>()}
    </select>

    <select id="getFileUrl" resultType="com.gok.pboot.portal.vo.SourceFileUrlVo">
        SELECT
            pam.source,
            sf.file_url
        FROM
            portal_application_manage AS pam
                JOIN sys_file AS sf ON pam.file_id = sf.id
        WHERE
            pam.del_flag = 0
          AND pam.source IS NOT NULL
          AND sf.file_url IS NOT NULL
    </select>

</mapper>