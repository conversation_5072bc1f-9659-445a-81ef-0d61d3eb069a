<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.ProjectFeedbackMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.ProjectFeedback">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="applicationId" column="application_id" jdbcType="BIGINT"/>
        <result property="applicationName" column="application_name" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="handlingSituation" column="handling_situation" jdbcType="TINYINT"/>
        <result property="fileIds" column="file_ids" jdbcType="VARCHAR"/>
        <result property="serviceAttitude" column="service_attitude" jdbcType="INTEGER"/>
        <result property="serviceEffectiveness" column="service_effectiveness" jdbcType="INTEGER"/>
        <result property="serviceEfficiency" column="service_efficiency" jdbcType="INTEGER"/>
        <result property="serviceFeel" column="service_feel" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="feedbackUserId" column="feedback_user_id" jdbcType="BIGINT"/>
        <result property="feedbackUserName" column="feedback_user_name" jdbcType="VARCHAR"/>
        <result property="sortOrder" column="sort_order" jdbcType="BIGINT"/>
    </resultMap>



    <update id="setSatisfaction">
        UPDATE project_feedback
        SET service_attitude      = #{satisfactionDto.serviceAttitude},
            service_effectiveness = #{satisfactionDto.serviceEffectiveness},
            service_efficiency    = #{satisfactionDto.serviceEfficiency},
            service_feel          = #{satisfactionDto.serviceFeel}
        WHERE id = #{satisfactionDto.id};
    </update>


    <select id="queryWOList" resultType="com.gok.pboot.portal.vo.WorkOrderPageVo">
        SELECT
        pf.id,pf.content,pf.type,pf.application_name,pf.feedback_user_name as createBy,pf.create_time,pf.handling_situation,pfc.followed_worker,pfc.zentao_url
        FROM project_feedback pf
        LEFT JOIN project_feedback_confirm pfc ON pf.id = pfc.feedback_id
        <where>
            pf.del_flag = ${@<EMAIL>()}
            <if test="dto.handlingSituation != null">
                and
                pf.handling_situation = #{dto.handlingSituation}
            </if>
            <if test="dto.type != null">
                and
                pf.type = #{dto.type}
            </if>
            <if test="dto.feedbackUserId != null and dto.feedbackUserId!=0">
                and
                pf.feedback_user_id = #{dto.feedbackUserId}
            </if>
            <if test="dto.followedWorkerId != null and dto.followedWorkerId !=0">
                and
                pfc.followed_worker_id = #{dto.followedWorkerId}
            </if>
        </where>
        ORDER BY
        CASE
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 1
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 1
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 1
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 2
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 3
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 4
        WHEN pf.handling_situation = ${@<EMAIL>} THEN 4
        ELSE 5
        END,
        pf.create_time DESC;
    </select>


    <select id="selNotClosed" resultType="com.gok.pboot.portal.db.entity.ProjectFeedback">
        SELECT pf.id,
               pf.content,
               pf.type,
               pf.application_name,
               pf.feedback_user_id,
               pf.feedback_user_name,
               pf.create_time,
               pf.handling_situation
        FROM project_feedback pf
        where pf.del_flag ='0'
        and pf.handling_situation ='1'
    </select>

    <update id="updateStatusByFeedbackId">
        UPDATE project_feedback
        SET handling_situation='4',
            update_time=NOW()
        WHERE id = #{feedbackId}
          AND del_flag = '0'
    </update>
</mapper>
