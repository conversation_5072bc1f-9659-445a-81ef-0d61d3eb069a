<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalApplicationCategoryMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.PortalApplicationCategory">
        <id column="id" property="id"/>
        <result column="category_name" property="categoryName"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="remarks" property="remarks"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <delete id="delFlagAll">
        UPDATE portal_application_category
        SET del_flag=${@<EMAIL>()},
            update_time=NOW(),
            update_by=#{userName}
    </delete>

    <select id="queryCategoryWithCount" resultType="com.gok.pboot.portal.vo.PortalApplicationCategoryVo">
        SELECT c.id, c.category_name, COUNT(m.category_id) applicationCount, c.sort_order
        FROM portal_application_category c
                 LEFT JOIN portal_application_manage m
                           ON c.id = m.category_id and m.del_flag = ${@<EMAIL>()}
        WHERE c.del_flag = ${@<EMAIL>()}
        GROUP BY c.id
        ORDER BY c.sort_order
    </select>

</mapper>