<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalHrmresourceMapper">
    <select id="selByUserId" resultType="com.gok.pboot.portal.vo.UserInfoVo">
        select id                          as id,
               alias_name                  as aliasName,
               work_code                   as workCode,
               mobile                      as phone,
               birthday                    as birthday,
               DATEDIFF(now(), start_date) as jobDays,
               start_date                  as startDate,
               job                         as job,
               position_level              as positionLevel
        from portal_hrmresource
                where del_flag = '0'
        <if test="userId != null">
            and id = #{userId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selByMobile" resultType="com.gok.pboot.portal.vo.UserInfoVo">
        select id                          as id,
               alias_name                  as aliasName,
               work_code                   as workCode,
               mobile                      as phone,
               birthday                    as birthday,
               DATEDIFF(now(), start_date) as jobDays,
               start_date                  as startDate,
               job                         as job,
               position_level              as positionLevel
        from portal_hrmresource
                where del_flag = '0'
        <if test="mobile != null">
            and mobile = #{mobile,jdbcType=VARCHAR}
        </if>
        order by start_date desc limit 1
    </select>


    <select id="selByPhone" resultType="com.gok.pboot.portal.db.entity.PortalHrmResource">
        select
            *
        from portal_hrmresource
        where del_flag = ${@<EMAIL>()}
        <if test="phone != null and phone != ''">
            and mobile = #{phone}
        </if>
    </select>


</mapper>