<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.CmsOperationLogMapper">

    <!--批量插入语句-->
    <insert id="batchSave">
        insert into cms_operation_log(
            id,
            content_id,
            dept_id,
            dept_name,
            user_id,
            user_name,
            operation_type,
            operation_content,
            create_by,
            create_time,
            tenant_id
        )values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.contentId},
            #{item.deptId},
            #{item.deptName},
            #{item.userId},
            #{item.userName},
            #{item.operationType},
            #{item.operationContent},
            #{item.createBy},
            #{item.createTime},
            #{item.tenantId}
            )
        </foreach>
    </insert>

    <!--根据id集合批量逻辑删除-->
    <delete id="deleteByIdList">
        delete from cms_operation_log
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--分页查询操作日志信息-->
    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        id,
        dept_name,
        user_name,
        operation_type,
        operation_content,
        create_time
        FROM cms_operation_log
        <where>
            <if test="contentId != null">
                AND content_id = #{contentId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!--通用查询映射-->
    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.CmsOperationLog">
        <id column="id" property="id"/>
        <result column="content_id" property="contentId"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="operation_type" property="operationType"/>
        <result column="operation_content" property="operationContent"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

</mapper>
