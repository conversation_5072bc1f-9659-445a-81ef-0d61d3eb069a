<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.ChatMessageMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.DifyChatMessages">
        <id column="id" property="id"/>
        <result column="user" property="user"/>
        <result column="user_id" property="userId"/>
        <result column="conversation_id" property="conversationId"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
            user,
            conversation_id
        FROM
            dify_chat_messages
        WHERE
            del_flag=${@<EMAIL>()} AND user_id = #{userId}
    </select>

</mapper>