<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.ProjectFeedbackLogMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.ProjectFeedbackLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="feedbackId" column="feedback_id" jdbcType="BIGINT"/>
            <result property="operationType" column="operation_type" jdbcType="TINYINT"/>
            <result property="operationContent" column="operation_content" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="operationUserName" column="operation_user_name" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryProjectFeedbackLogByFeedbackID" resultMap="BaseResultMap">
        select operation_user_name as create_by,update_time,create_time,operation_type,operation_content from project_feedback_log
        where feedback_id = #{feedbackID}
          AND del_flag=${@<EMAIL>()}

    </select>

        <select id="queryAllByFeedbackId" resultType="com.gok.pboot.portal.vo.ProjectFeedbackLogVo">
            SELECT operation_user_name as createBy,create_time,operation_content,operation_type
            FROM project_feedback_log
            WHERE feedback_id=#{feedbackId} AND del_flag=${@<EMAIL>()}

        </select>


</mapper>
