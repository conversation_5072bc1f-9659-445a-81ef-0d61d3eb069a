<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.CmsContentFileMapper">

    <!--分页查询内容信息-->
    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        id,
        content_id,
        file_type,
        file_id,
        video_key,
        video_progress,
        create_by,
        create_time
        FROM cms_content_file
        <where>
            <if test="contentId != null">
                AND content_id = #{contentId}
            </if>
            AND del_flag = ${@<EMAIL>()}
        </where>
        order by create_time desc
    </select>

    <!--逻辑删除-->
    <update id="logicDeleteByContentId">
        UPDATE cms_content_file
        SET del_flag = ${@<EMAIL>()}
        WHERE content_id = #{contentId}
    </update>

    <!--逻辑删除-->
    <update id="logicDeleteByIdList">
        UPDATE cms_content_file
        SET del_flag = ${@<EMAIL>()}
        <where>
            <if test="idList != null and idList.size() != 0">
                id IN
                <foreach collection="idList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <!--批量插入语句-->
    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO cms_content_file(
        id,
        content_id,
        file_type,
        file_id,
        video_key,
        video_progress,
        create_by,
        update_by,
        create_time,
        update_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.contentId},
            #{item.fileType},
            #{item.fileId},
            #{item.videoKey},
            #{item.videoProgress},
            #{item.createBy},
            #{item.updateBy},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.CmsContentFile">
        <id column="id" property="id"/>
        <result column="content_id" property="contentId"/>
        <result column="file_type" property="fileType"/>
        <result column="file_id" property="fileId"/>
        <result column="video_key" property="videoKey"/>
        <result column="video_progress" property="videoProgress"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="selByContentId" resultType="com.gok.pboot.portal.db.entity.CmsContentFile">
        select id,
        content_id,
        file_type,
        file_id,
        video_key,
        video_progress
        from cms_content_file
        where
        del_flag = '0'
        <if test="contentId != null">
            and content_id = #{contentId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selByContentIdList" resultMap="BaseResultMap">
        select id,
        content_id,
        file_type,
        file_id,
        video_key,
        video_progress
        from cms_content_file
        where
        del_flag = '0'
        and content_id in
        <foreach collection="contentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selTransNotCompleted" resultType="com.gok.pboot.portal.db.entity.CmsContentFile">
        select id, content_id, file_id, video_key, video_progress
        from cms_content_file
        where file_type = '2'
          and del_flag = '0'
          and video_progress !='100'
    </select>

    <update id="batchEditProgress">
        update cms_content_file
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="video_progress = case" suffix="end,">
                <foreach collection="transProgressList" item="item" index="index">
                    when video_key = #{item.videoKey,jdbcType=VARCHAR} then #{item.transCode,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where video_key in
        <foreach collection="transProgressList" item="item" open="(" close=")" separator=",">
            #{item.videoKey,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateTransProgressByKey">
        UPDATE
            cms_content_file
        SET
            video_progress = #{transProcess}
        WHERE
            video_key = #{videoKey} AND del_flag = '0'
    </update>

</mapper>