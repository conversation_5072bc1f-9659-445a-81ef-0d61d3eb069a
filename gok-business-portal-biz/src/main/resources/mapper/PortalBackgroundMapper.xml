<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalBackgroundMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.PortalBackground">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="birthday" property="birthday"/>
        <result column="anniversary" property="anniversary"/>
        <result column="status" property="status"/>
        <result column="portal_file_id" property="portalFileId"/>
        <result column="person_file_id" property="personFileId"/>
        <result column="weight" property="weight"/>
        <result column="default_flag" property="defaultFlag"/>
        <result column="remarks" property="remarks"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="app_file_id" property="appFileId"/>
    </resultMap>

    <!--根据id编辑内容上下架状态-->
    <update id="updateStatusById">
        UPDATE portal_background
        SET `status`    = #{status},
            update_by   = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
        AND default_flag != ${@<EMAIL>()}
    </update>

    <!--根据id集合批量逻辑删除-->
    <update id="logicDeleteByIdList">
        UPDATE portal_background
        SET del_flag = ${@<EMAIL>()}
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND default_flag != ${@<EMAIL>()}
    </update>

    <!--分页查询门户背景信息-->
    <select id="findPage" resultMap="BaseResultMap">
        select
            id,
            title,
            start_time,
            end_time,
            birthday,
            anniversary,
            status,
            portal_file_id,
            person_file_id,
            weight,
            default_flag,
            create_user_name,
            create_by,
            create_time,
            app_file_id
        from portal_background
        <where>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.title != null and query.title != ''">
                AND title like CONCAT('%',#{query.title},'%')
            </if>
            AND del_flag = ${@<EMAIL>()}
        </where>
        order by default_flag desc, weight desc, create_time desc
    </select>

    <!--查询用户适配背景-->
    <select id="find" resultMap="BaseResultMap">
        select
        id,
        title,
        start_time,
        end_time,
        birthday,
        anniversary,
        status,
        portal_file_id,
        person_file_id,
        app_file_id,
        weight,
        create_user_name,
        create_by,
        create_time,
        default_flag
        from portal_background
        <where>
            <if test="birthday != null">
                AND birthday = #{birthday}
            </if>
            <if test="anniversary != null">
                AND anniversary = #{anniversary}
            </if>
            <if test="defaultFlag != null">
                AND default_flag = #{defaultFlag}
            </if>
            <if test="defaultFlag == null and birthday == null and anniversary == null">
                AND (curdate() between start_time and end_time OR (start_time IS NULL AND end_time IS NULL))
            </if>
            AND del_flag = ${@<EMAIL>()}
            AND status = ${@<EMAIL>()}
        </where>
        ORDER BY weight DESC, create_time DESC
        LIMIT 1
    </select>

    <select id="findNormal" resultMap="BaseResultMap">
        select
        id,
        title,
        start_time,
        end_time,
        birthday,
        anniversary,
        status,
        portal_file_id,
        person_file_id,
        app_file_id,
        weight,
        create_by,
        create_time,
        default_flag
        from portal_background
        <where>
            del_flag = ${@<EMAIL>()}
            AND anniversary = ${@<EMAIL>()}
            AND default_flag = ${@<EMAIL>()}
            AND birthday = ${@<EMAIL>()}
            AND (curdate() between start_time and end_time OR (start_time IS NULL AND end_time IS NULL))
            AND status = ${@<EMAIL>()}
            <if test="weight != null">
                AND weight > #{weight}
            </if>
        </where>
        ORDER BY weight DESC, create_time DESC
        LIMIT 1
    </select>

</mapper>