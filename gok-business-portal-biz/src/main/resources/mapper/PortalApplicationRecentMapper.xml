<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalApplicationRecentMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.PortalApplicationRecent">
            <id property="id" column="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId"/>
        <result column="application_id" property="applicationId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,application_id,
        create_by,create_time,tenant_id
    </sql>
    <update id="updateRecent">
        UPDATE portal_application_recent
        SET create_time = NOW()
        WHERE user_id = #{userId}
          AND application_id = #{applicationId}
          AND create_time = (
            SELECT MAX(create_time)
            FROM (
                     SELECT create_time
                     FROM portal_application_recent
                     WHERE application_id = #{applicationId} AND user_id = #{userId}
                 ) AS subquery
        );
    </update>

    <select id="recentList" resultType="com.gok.pboot.portal.vo.ApplicationIconVo">
        SELECT  DISTINCT par.application_id,
               pam.belong_application_id,
               pam.application_name,
               pam.file_id,
               sf.file_url,
               pam.category_id,
               pam.link_address
        FROM portal_application_recent AS par
                 JOIN portal_application_manage AS pam ON par.application_id = pam.id
                 LEFT JOIN sys_file AS sf ON pam.file_id = sf.id
        WHERE par.user_id = #{userId}
          AND pam.del_flag = ${@<EMAIL>()}
          AND pam.`status` = ${@<EMAIL>()}
          AND par.create_time = (
            SELECT MAX(create_time)
            FROM portal_application_recent
            WHERE application_id = par.application_id
              And user_id = #{userId}
        )
        ORDER BY par.create_time DESC LIMIT 7;
    </select>
    <select id="countByUserAppId" resultType="java.lang.Integer">
       select count(*)
       from portal_application_recent
       where user_id = #{userId}
       and application_id = #{applicationId}


    </select>
</mapper>
