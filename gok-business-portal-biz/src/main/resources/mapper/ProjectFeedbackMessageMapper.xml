<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.ProjectFeedbackMessageMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.ProjectFeedbackMessage">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="triggerType" column="trigger_type" jdbcType="TINYINT"/>
            <result property="feedbackId" column="feedback_id" jdbcType="BIGINT"/>
            <result property="recipient" column="recipient" jdbcType="VARCHAR"/>
            <result property="recipientId" column="recipient_id" jdbcType="BIGINT"/>
            <result property="messageContent" column="message_content" jdbcType="VARCHAR"/>
            <result property="zentaoUrl" column="zentao_url" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>
    <select id="getByFeedbackIdAndType" resultType="com.gok.pboot.portal.db.entity.ProjectFeedbackMessage">
        select feedback_id,message_content,zentao_url,trigger_type from project_feedback_message
        where feedback_id = #{feedbackId}
            and trigger_type = #{triggerType}
        and del_flag = ${@<EMAIL>()}
        ORDER BY create_time ASC
        LIMIT 1;
    </select>
</mapper>
