<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalSysFileMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.PortalSysFile">
    <!--@mbg.generated-->
    <!--@Table sys_file-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="bucket_name" jdbcType="VARCHAR" property="bucketName" />
    <result column="original" jdbcType="VARCHAR" property="original" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="file_state" jdbcType="BOOLEAN" property="fileState" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
  </resultMap>

    <select id="selByIds" resultType="com.gok.pboot.portal.db.entity.PortalSysFile">
        select id, file_name, file_size
        from sys_file where del_flag = '0'
        <if test="fileIds != null and fileIds.size() != 0">
            and id in
            <foreach collection="fileIds" index="," open="(" close=")" item="id">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

</mapper>