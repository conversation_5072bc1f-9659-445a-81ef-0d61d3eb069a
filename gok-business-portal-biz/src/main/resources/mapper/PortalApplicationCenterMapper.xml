<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.PortalApplicationCenterMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.PortalApplicationCenter">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="application_id" property="applicationId"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="queryUsefulApplication" resultType="com.gok.pboot.portal.vo.ApplicationIconVo">
        SELECT pac.application_id,
               pam.belong_application_id,
               pam.application_name,
               pam.file_id,
               sf.file_url,
               pam.category_id,
               pam.link_address
        FROM portal_application_center AS pac
                 JOIN portal_application_manage AS pam
                 LEFT JOIN sys_file AS sf ON pam.file_id = sf.id
        WHERE pac.application_id = pam.id
          AND pac.user_id = #{userId}
          AND pam.del_flag=${@<EMAIL>()}
          AND pam.`status`=${@<EMAIL>()}
        ORDER BY pac.sort_order
    </select>

    <select id="queryNoUsefulByCategoryId" resultType="com.gok.pboot.portal.vo.ApplicationIconVo">
        SELECT pam.id AS application_id,
        pam.application_name,
        pam.file_id,
        sf.file_url,
        pam.category_id,
        pam.link_address,
        pam.belong_application_id
        FROM portal_application_manage AS pam
        LEFT JOIN sys_file AS sf ON pam.file_id=sf.id
        <where>
            pam.del_flag=${@<EMAIL>()}
            AND pam.category_id=#{categoryId}
            AND pam.`status`=${@<EMAIL>()}
            <if test="applicationIds != null and applicationIds.size() > 0">
            AND pam.belong_application_id IN
            <foreach collection="applicationIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
           </if>
        </where>
        ORDER BY pam.weight DESC,pam.lunch_time DESC
    </select>

    <select id="queryAllByCategoryIds" resultType="com.gok.pboot.portal.vo.ApplicationViewVo">
        SELECT pam.id AS application_id,
        pam.application_name,
        pam.file_id,
        sf.file_url,
        pam.category_id,
        pam.link_address,
        pam.belong_application_id
        FROM portal_application_manage AS pam
        LEFT JOIN sys_file AS sf ON pam.file_id=sf.id
        <where>
            pam.del_flag=${@<EMAIL>()}
            AND pam.category_id=#{categoryId}
            AND pam.`status`=${@<EMAIL>()}
            <if test="applicationIds != null and applicationIds.size() > 0">
            AND pam.belong_application_id IN
            <foreach collection="applicationIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            </if>
        </where>
        ORDER BY pam.weight DESC,pam.lunch_time DESC

    </select>
</mapper>