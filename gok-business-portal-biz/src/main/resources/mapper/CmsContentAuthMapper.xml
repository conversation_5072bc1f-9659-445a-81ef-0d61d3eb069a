<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.CmsContentAuthMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.CmsContentAuth">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="contentId" column="content_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,content_id,user_id,
        create_time,create_by,dept_id
    </sql>
    <insert id="batchSaveByUserId">
        insert into cms_content_auth(
         content_id,
         user_id,
         create_by,
         create_time
        )VALUES
        <foreach collection="userIdList" item="item" separator=",">
            (
            #{contentId},
            #{item},
            #{createBy},
            ${'NOW()'}
            )
        </foreach>

    </insert>
    <insert id="batchSaveByDeptId">
        insert into cms_content_auth(
        content_id,
        dept_id,
        create_by,
        create_time
        )VALUES
        <foreach collection="deptIdList" item="item" separator=",">
            (
            #{contentId},
            #{item},
            #{createBy},
            ${'NOW()'}
            )
        </foreach>

    </insert>
    <delete id="batchDeleteByContentId">
        delete from cms_content_auth
        where content_id = #{contentId}
    </delete>
    <delete id="batchDeleteByContentIdList">
        delete from cms_content_auth
        where content_id in
        <foreach collection="contentIdList" item="c" open="(" separator="," close=")">
            #{c}
        </foreach>
    </delete>
    <select id="getAuthContentByUserId" resultType="java.lang.Long">
        select content_id from
        cms_content_auth
        where user_id = #{userId}
    </select>
    <select id="getDeptIdListByContentId" resultType="java.lang.Long">
        select
        dept_id
        from
        cms_content_auth
        where
        content_id = #{contentId}
    </select>
    <select id="getUserIdListByContentId" resultType="java.lang.Long">
        select
            user_id
        from
            cms_content_auth
        where
            content_id = #{contentId}
    </select>
    <select id="isAuthByDeptId" resultType="java.lang.Integer">
        select
            count(*)
        from
            cms_content_auth
        where
            content_id = #{contentId}
        and dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>
    <select id="isAuthByUserId" resultType="java.lang.Integer">
        select
        count(*)
        from
        cms_content_auth
        where
        content_id = #{contentId}
        and user_id = #{userId}
    </select>

</mapper>
