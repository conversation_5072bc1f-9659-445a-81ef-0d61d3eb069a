<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.CmsContentMapper">

    <!--分页查询内容信息-->
    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        id,
        title,
        business_type,
        status,
        publisher_id,
        publisher_name,
        publish_time,
        create_by,
        show_flag,
        create_time,
        relation_content_id,
        link_address,
        content_text,
        cover_image_url,
        weight,
        remarks,
        create_user_name,
        content_type,
        content_category,
        send_obj,
        is_send
        FROM cms_content
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.contentCategoryList != null and query.contentCategoryList.size() != 0">
                AND content_category IN
                <foreach collection="query.contentCategoryList" item="contentCategory" open="(" separator="," close=")">
                    #{contentCategory}
                </foreach>
            </if>
            <if test="query.businessTypeList != null and query.businessTypeList.size() != 0">
                AND business_type IN
                <foreach collection="query.businessTypeList" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="query.title != null and query.title != ''">
                AND title like CONCAT('%',#{query.title},'%')
            </if>
            <if test="query.publishTime != null and query.publishTime != ''">
                AND ( publish_time <![CDATA[ <= ]]> #{query.publishTime} OR publish_time IS NULL )
            </if>
            <if test="query.showFlag != null">
                AND show_flag = #{query.showFlag}
            </if>
        </where>
        ORDER BY weight DESC
        <choose>
            <when test="query.orderType != null and query.orderType == 0">, create_time DESC</when>
            <when test="query.orderType != null and query.orderType == 1">, launch_time DESC</when>
            <otherwise>, create_time DESC</otherwise>
        </choose>
    </select>

    <select id="findPageByAuth" resultType="com.gok.pboot.portal.db.entity.CmsContent">
        SELECT DISTINCT
        c.id,
        c.title,
        c.business_type,
        c.STATUS,
        c.publisher_id,
        c.publisher_name,
        c.publish_time,
        c.create_by,
        c.show_flag,
        c.create_time,
        c.relation_content_id,
        c.link_address,
        c.content_text,
        c.cover_image_url,
        c.weight,
        c.remarks,
        c.create_user_name,
        c.content_type,
        c.content_category,
        c.send_obj,
        c.is_send
        FROM
        cms_content c
        LEFT JOIN
        cms_content_auth ca ON c.id = ca.content_id
        <where>
            c.del_flag = ${@<EMAIL>()}
            AND (
            c.send_obj = ${@<EMAIL>()}
            OR (
            c.send_obj = ${@<EMAIL>()}
            <if test="deptIdList != null and deptIdList.size() > 0">
                AND ca.dept_id IN
                <foreach collection="deptIdList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            )
            OR ( c.send_obj = ${@<EMAIL>()} AND ca.user_id = #{userId} )
            )
            <if test="query.status != null">
                AND c.status = #{query.status}
            </if>
            <if test="query.contentCategoryList != null and query.contentCategoryList.size() != 0">
                AND c.content_category IN
                <foreach collection="query.contentCategoryList" item="contentCategory" open="(" separator="," close=")">
                    #{contentCategory}
                </foreach>
            </if>
            <if test="query.businessTypeList != null and query.businessTypeList.size() != 0">
                AND c.business_type IN
                <foreach collection="query.businessTypeList" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="query.title != null and query.title != ''">
                AND c.title like CONCAT('%',#{query.title},'%')
            </if>
            <if test="query.publishTime != null and query.publishTime != ''">
                AND ( c.publish_time <![CDATA[ <= ]]> #{query.publishTime} OR c.publish_time IS NULL )
            </if>
            <if test="query.showFlag != null">
                AND c.show_flag = #{query.showFlag}
            </if>
        </where>
        ORDER BY c.weight DESC
        <choose>
            <when test="query.orderType != null and query.orderType == 0">, c.create_time DESC</when>
            <when test="query.orderType != null and query.orderType == 1">, c.launch_time DESC</when>
            <otherwise>, c.create_time DESC</otherwise>
        </choose>
    </select>

    <!--根据id查询内容信息-->
    <select id="findByIdList" resultMap="BaseResultMap">
        SELECT
        *
        FROM cms_content
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="idList != null and idList.size() != 0">
                AND id IN
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.contentCategoryList != null and query.contentCategoryList.size() != 0">
                AND content_category IN
                <foreach collection="query.contentCategoryList" item="contentCategory" open="(" separator="," close=")">
                    #{contentCategory}
                </foreach>
            </if>
            <if test="query.businessTypeList != null and query.businessTypeList.size() != 0">
                AND business_type IN
                <foreach collection="query.businessTypeList" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="query.title != null and query.title != ''">
                AND title like CONCAT('%',#{query.title},'%')
            </if>
            <if test="query.publishTime != null and query.publishTime != ''">
                AND ( publish_time <![CDATA[ <= ]]> #{query.publishTime} OR publish_time IS NULL )
            </if>
            <if test="query.showFlag != null">
                AND show_flag = #{query.showFlag}
            </if>
        </where>
        ORDER BY weight DESC, create_time DESC
    </select>

    <!--根据id编辑内容上下架状态-->
    <update id="updateStatusById">
        UPDATE cms_content
        SET `status`    = #{status},
            update_by   = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!--根据id编辑内容上下架状态-->
    <update id="updateShowFlagById">
        UPDATE cms_content
        SET `show_flag` = #{showFlag},
            update_by   = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!--根据id集合批量逻辑删除-->
    <update id="logicDeleteByIdList">
        UPDATE cms_content
        SET del_flag = ${@<EMAIL>()}
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--根据内容id集合批量变更关联关系-->
    <update id="updateRelationByIdList">
        UPDATE cms_content
        SET relation_content_id = 0
        WHERE
        content_category = ${@<EMAIL>()}
        AND relation_content_id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--通用查询映射-->
    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.CmsContent">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="business_type" property="businessType"/>
        <result column="content_category" property="contentCategory"/>
        <result column="content_type" property="contentType"/>
        <result column="publisher_id" property="publisherId"/>
        <result column="publisher_name" property="publisherName"/>
        <result column="publish_time" property="publishTime"/>
        <result column="status" property="status"/>
        <result column="content_text" property="contentText"/>
        <result column="relation_content_id" property="relationContentId"/>
        <result column="link_address" property="linkAddress"/>
        <result column="cover_image_url" property="coverImageUrl"/>
        <result column="weight" property="weight"/>
        <result column="remarks" property="remarks"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="show_flag" property="showFlag"/>
    </resultMap>

    <select id="findTrainingPlan" resultType="com.gok.pboot.portal.db.entity.CmsContent">
        SELECT id,
               title,
               business_type,
               status,
               publisher_id,
               publisher_name,
               create_by,
               create_time,
               relation_content_id,
               link_address,
               weight,
               remarks,
               content_type
        FROM cms_content
        where del_flag = '0'
          and status = '0'
          and content_category = '3'
    </select>

    <update id="updateCoverUrl">
        update cms_content
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="cover_image_url = case" suffix="end,">
                <foreach collection="coverUrlDtoList" item="item" index="index">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.coverImageUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="show_flag = case" suffix="end,">
                <foreach collection="coverUrlDtoList" item="item" index="index">
                    when id = #{item.id,jdbcType=BIGINT} then 0
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="coverUrlDtoList" item="item" open="(" close=")" separator=",">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateCoverUrlByKey">
        UPDATE
            cms_content
        SET cover_image_url = #{coverImageUrl}
        WHERE id = #{id,jdbcType=BIGINT}
          AND show_flag = 0
          AND del_flag = ${@<EMAIL>()}
    </update>
    <update id="changeSendStatus">
        update cms_content
        set is_send = ${@<EMAIL>()}
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getByPublishTime" resultType="com.gok.pboot.portal.db.entity.CmsContent">
        SELECT id,
               title,
               business_type,
               content_category,
               status,
               publisher_id,
               publisher_name,
               create_by,
               create_time,
               relation_content_id,
               link_address,
               weight,
               remarks,
               is_send,
               send_obj,
               publish_type
               content_type FROM cms_content
        WHERE publish_time BETWEEN DATE_SUB(NOW(), INTERVAL 5 MINUTE) + INTERVAL 1 SECOND AND NOW()
        AND is_send = ${@com.gok.pboot.portal.enums.CmsSendEnum@NO_SEND.getValue()}
        AND status = 0
    </select>


    <select id="findSortCount" resultType="com.gok.pboot.portal.vo.CmsSortCountVo">
        SELECT
        (CASE
        WHEN c.business_type = 4 THEN 'systems'
        WHEN c.business_type = 5 THEN 'standards'
        WHEN c.business_type = 6 THEN 'processes'
        WHEN c.business_type = 7 THEN 'mechanisms'
        WHEN c.business_type = 8 THEN 'plans'
        WHEN c.business_type = 9 THEN 'notifications'
        WHEN c.business_type = 10 THEN 'decision'
        END) AS businessType,
        COUNT(DISTINCT c.id) AS count
        FROM
        cms_content c
        LEFT JOIN cms_content_auth ca ON c.id = ca.content_id
        WHERE
        c.del_flag = ${@<EMAIL>()}
        AND (
        c.send_obj = ${@<EMAIL>()}
        OR (
        c.send_obj = ${@<EMAIL>()} 	AND ca.dept_id
        IN
        <foreach collection="deptIdList" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        )
        OR ( c.send_obj = ${@<EMAIL>()} AND ca.user_id = #{userId} )
        )
        AND c.status = 0
        AND c.content_category = 2
        AND (
        c.publish_time &lt; NOW()
        OR c.publish_time IS NULL
        )
        AND c.business_type IN (4, 5, 6, 7, 8, 9, 10)
        GROUP BY
        c.business_type;
    </select>

</mapper>
