<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.ProjectFeedbackResponseMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.ProjectFeedbackResponse">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="feedbackId" column="feedback_id" jdbcType="BIGINT"/>
            <result property="responseContent" column="response_content" jdbcType="VARCHAR"/>
            <result property="fileIds" column="file_ids" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>

    <update id="updateResponse">
        UPDATE project_feedback_response
        SET response_content=#{content},file_ids=#{fileIds},update_by=#{updateBy},update_time=NOW()
        WHERE feedback_id=#{feedbackId} AND del_flag=${@<EMAIL>()}
    </update>

    <select id="queryProjectFeedbackResponseByFeedbackID" resultMap="BaseResultMap">
        select response_content,update_time,file_ids from project_feedback_response
        where feedback_id = #{feedbackID} AND del_flag=${@<EMAIL>()}
    </select>

    <select id="getOneByFeedbackId" resultType="com.gok.pboot.portal.db.entity.ProjectFeedbackResponse">
        SELECT id,feedback_id,response_content,file_ids,create_by,update_by,create_time,update_time,del_flag,tenant_id
        FROM project_feedback_response
        WHERE feedback_id=#{feedbackId} AND del_flag=${@<EMAIL>()}
    </select>


</mapper>
