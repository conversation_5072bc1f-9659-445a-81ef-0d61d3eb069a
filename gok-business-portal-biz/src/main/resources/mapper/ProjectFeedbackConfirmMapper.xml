<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.portal.db.mapper.ProjectFeedbackConfirmMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.portal.db.entity.ProjectFeedbackConfirm">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="feedbackId" column="feedback_id" jdbcType="BIGINT"/>
            <result property="followedWorker" column="followed_worker" jdbcType="VARCHAR"/>
            <result property="followedWorkerId" column="followed_worker_id" jdbcType="BIGINT"/>
            <result property="handleWorker" column="handle_worker" jdbcType="VARCHAR"/>
            <result property="handleWorkerId" column="handle_worker_id" jdbcType="BIGINT"/>
            <result property="urgencyDegree" column="urgency_degree" jdbcType="TINYINT"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="zentaoUrl" column="zentao_url" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>
    <select id="getOneByFeedbackId" resultType="com.gok.pboot.portal.db.entity.ProjectFeedbackConfirm">
        SELECT id,feedback_id,followed_worker,followed_worker_id,handle_worker,handle_worker_id,urgency_degree,remarks,zentao_id,zentao_url,create_by,update_by,create_time,update_time,del_flag,tenant_id
        FROM project_feedback_confirm
        WHERE feedback_id=#{feedbackId} AND del_flag=${@<EMAIL>()}
        order by create_time
        limit 1
        </select>

    <update id="updateByFeedBackId">
        UPDATE project_feedback_confirm
        SET followed_worker_id=#{followed_worker_id},followed_worker=#{followed_worker},update_by=#{userName},update_time=NOW()
        WHERE feedback_id=#{feedbackId} AND del_flag=${@<EMAIL>()}
    </update>

    <update id="generalUpdate">
        UPDATE project_feedback_confirm
        SET followed_worker_id=#{followed_worker_id},followed_worker=#{followed_worker},update_by=#{userName},update_time=NOW(),urgency_degree=#{status}
        WHERE feedback_id=#{feedbackId} AND del_flag=${@<EMAIL>()}
    </update>

</mapper>
